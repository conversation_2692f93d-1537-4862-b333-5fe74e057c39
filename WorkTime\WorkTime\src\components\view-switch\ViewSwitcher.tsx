import React from "react";
import styled from "styled-components";
import Container from "../Container";
import listviewIcon from "../../assets/images/view-switch/listview-icon-inactive.svg";
import listIconHover from "../../assets/images/view-switch/listview-icon-hover.svg";
import listIconActive from "../../assets/images/view-switch/listview-icon-active.svg";
import gridviewIcon from "../../assets/images/view-switch/gridview-icon-inactive.svg";
import gridIconHover from "../../assets/images/view-switch/gridview-icon-hover.svg";
import gridIconActive from "../../assets/images/view-switch/gridview-icon-active.svg";

const SideSpaceStyled = styled(Container)`
  display: flex;
  flex-direction: row;
  position: absolute;
  width: 3rem;
  top: 12%;
  right: 1rem;
  justify-content: space-between;
`;

const ImageListView = styled(Container)<{ $isListView?: boolean }>`
  display: inline-block;
  position: relative;
  background-size: cover;
  height: 1.25rem;
  width: 1.304rem;
  background-image: ${({ $isListView }) =>
    $isListView ? `url(${listIconActive})` : `url(${listviewIcon})`};
  cursor: pointer;

  &:hover {
    background-image: url(${listIconHover});
  }
`;

const ImageGridView = styled(Container)<{ $isListView?: boolean }>`
  background-size: cover;
  height: 1.3rem;
  width: 1.304rem;
  background-image: ${({ $isListView }) =>
    !$isListView ? `url(${gridIconActive})` : `url(${gridviewIcon})`};
  cursor: pointer;
  &:hover {
    background-image: url(${gridIconHover});
  }
`;

interface SideSpaceProps {
  isListView: boolean;
  onClickListView: () => void;
  onClickGridView: () => void;
}

const SideSpace: React.FC<SideSpaceProps> = ({
  isListView,
  onClickListView,
  onClickGridView,
}) => {
  return (
    <SideSpaceStyled>
      <ImageListView $isListView={isListView} onClick={onClickListView} />
      <ImageGridView $isListView={isListView} onClick={onClickGridView} />
    </SideSpaceStyled>
  );
};

export default SideSpace;
