const getViteEnv = (): Record<string, string | undefined> => {
  try {
    const viteImportMeta = (0, eval)("import.meta");
    return (viteImportMeta && viteImportMeta.env) || {};
  } catch {
    return {};
  }
};

const getEnvValue = (key: string): string | undefined => {
  const viteEnv = getViteEnv();
  const valueFromVite = (viteEnv as any)[key];
  if (valueFromVite !== undefined && valueFromVite !== null) {
    return valueFromVite as string | undefined;
  }
  if (
    typeof process !== "undefined" &&
    typeof (process as any).env !== "undefined"
  ) {
    return ((process as any).env as any)[key];
  }
  return undefined;
};

const gatewayApi = () => {
  switch (import.meta.env.VITE_GATEWAY_API) {
    case "Dev":
      return "https://localhost:7056/";
    case "LocalDocker":
      return "http://host.docker.internal:8080/";
    case "Testing":
      return "http://*************:8080/";
    case "Production":
      return "https://worktime.bg/gateway-api/";
    default:
      return "http://localhost:7056/";
  }
};

export const GATEWAY_API_PATH = gatewayApi();

const worktimeApi = () => {
  switch (import.meta.env.VITE_WORKTIME_API) {
    case "Dev":
      return "https://localhost:7124/";
    case "LocalDocker":
      return "http://host.docker.internal:8082/";
    case "Testing":
      return "http://*************:8082/";
    case "Production":
      return "https://worktime.bg/worktime-api/";
    default:
      return "https://localhost:7124/";
  }
};

export const WORKTIME_API_PATH = worktimeApi();
