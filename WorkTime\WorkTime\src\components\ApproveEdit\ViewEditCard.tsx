import React from "react";
import styled from "styled-components";
import Translator from "../../services/language/Translator";

interface ViewEditCardProps {
  previousValue: string | undefined;
}

const CardContainer = styled.div`
  background-color: var(--auth-button-background-color);
  border-radius: 1.5rem;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  min-width: 11rem;
  max-width: 20rem;
  box-sizing: border-box;
`;

const HeaderText = styled.p`
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  color: var(--combobox-header-text-color);
  font-weight: normal;
`;

const ValueText = styled.p`
  margin: 0;
  font-size: 1rem;
  color: var(--profile-department-name-font-color);
  font-weight: 500;
  line-height: 1.4;
`;

const ViewEditCard: React.FC<ViewEditCardProps> = ({ previousValue }) => {
  return (
    <CardContainer>
      <HeaderText>
        <Translator getString="Before editing:" />
      </HeaderText>
      <ValueText>{previousValue}</ValueText>
    </CardContainer>
  );
};

export default ViewEditCard;
