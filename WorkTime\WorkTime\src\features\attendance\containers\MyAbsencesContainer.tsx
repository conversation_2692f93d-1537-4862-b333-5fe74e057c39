import React, { useEffect, useState } from "react";
import { AbsencesPageEmployeesList } from "../AbsencesPageEmploeesList";
import airplane from "../../../assets/images/attendancies/airplane-big.svg";
import heart from "../../../assets/images/attendancies/hearth-big.svg";
import styled from "styled-components";
import {
  ButtonContainer,
  CardContainer,
  Icon,
  MainCardContainer,
  StyledButton,
  StyledLabel,
  TopContainer,
} from "../styles";
import { Employee } from "../useFilteredEmployees";
import { useAppSelector } from "../../../app/hooks";
import { AbsenceHospitalDTO } from "../../../models/DTOs/absence/AbsenceHospitalDTO";
import { selectPayrolls } from "../../payroll/payrollsActions";
import { calculateDaysInSelectedYear } from "../../../services/calendar/calendarService";
import { AbsenceStatus } from "../../../models/DTOs/absence/AbsenceStatus";
import { EventType } from "../../../models/DTOs/absence/EventType";
import { LightPayrollDTO } from "../../../models/DTOs/payrolls/LightPayrollDTO";

const CustomIcon = styled(Icon)`
  margin-top: 0.4em;
`;

interface MyAbsencesContainerProps {
  selectedPayroll?: LightPayrollDTO;
  selectedYear?: number;
  selectedMonth?: number;
  handleAddAbsence: () => void;
  selectedEmployee?: Employee;
  onSelectEmployee: (employee: Employee | undefined) => void;
}

export const MyAbsencesContainer: React.FC<MyAbsencesContainerProps> = ({
  selectedPayroll,
  selectedYear,
  selectedMonth,
  handleAddAbsence,
  selectedEmployee,
  onSelectEmployee,
}) => {
  const payrollsState = useAppSelector(selectPayrolls);
  const [usedSickLeaveDays, setUsedSickLeaveDays] = useState(0);

  const leaves = payrollsState.payrolls
    .filter((payroll) => payroll.id === selectedPayroll?.workTimeId)
    .map((payroll) => payroll.leaves)
    .flat();

  useEffect(() => {
    setUsedSickLeaveDays(
      leaves
        .filter(
          (leave) =>
            leave.isHospital === true && leave.status === AbsenceStatus.Approved
        )
        .reduce(
          (total: number, leave) =>
            total +
            calculateDaysInSelectedYear(leave, selectedYear?.toString() ?? ""),
          0
        )
    );
  }, [leaves, selectedPayroll]);

  const usedLeaveDays = leaves
    .filter(
      (leave: AbsenceHospitalDTO) =>
        leave.isHospital === false &&
        leave.status === AbsenceStatus.Approved &&
        leave.typeIdentifier === EventType.ПлатенГодишенОтпуск
    )
    .reduce((total: number, leave: AbsenceHospitalDTO) => {
      return (
        total +
        calculateDaysInSelectedYear(leave, selectedYear?.toString() ?? "")
      );
    }, 0);

  const remainingPaidLeaveDays =
    (selectedPayroll?.annualPaidLeave ?? 0) +
    (selectedPayroll?.additionalAnnualPaidLeave ?? 0) +
    (selectedPayroll?.annualPaidLeavePastYears ?? 0) -
    usedLeaveDays;

  return (
    <>
      <TopContainer data-testid="top-container">
        <MainCardContainer data-testid="main-card-container-1">
          <CustomIcon
            src={airplane}
            size="medium"
            alt="airplane"
            data-testid="icon-airplane"
          />
          <CardContainer
            variant="AbsencesContainer"
            data-testid="card-container-1"
          >
            <StyledLabel
              children={
                remainingPaidLeaveDays < 0
                  ? "0"
                  : remainingPaidLeaveDays.toString()
              }
              boldness={700}
              fontSize={22}
              color="var(--attendancies-right-view-card-container-font)"
              data-testid="label-remaining-days"
            />
            <StyledLabel
              children={remainingPaidLeaveDays == 1 ? "day" : "days"}
              boldness={300}
              fontSize={14}
              color="var(--attendancies-right-view-card-container-font)"
              data-testid="label-days"
            />
            <StyledLabel
              children="remaining"
              boldness={400}
              fontSize={16}
              color="var(--attendancies-right-view-card-container-font)"
              data-testid="label-remaining"
            />
            <StyledLabel
              children="Paid Leave"
              boldness={400}
              fontSize={16}
              color="var(--attendancies-right-view-card-container-font)"
              data-testid="label-paid-leave"
            />
          </CardContainer>
        </MainCardContainer>
        <MainCardContainer data-testid="main-card-container-2">
          <CustomIcon
            src={heart}
            size="medium"
            alt="heart"
            data-testid="icon-heart"
          />
          <CardContainer
            variant="AbsencesContainer"
            data-testid="card-container-2"
          >
            <StyledLabel
              children={
                usedSickLeaveDays < 0 ? "0" : usedSickLeaveDays.toString()
              }
              boldness={700}
              fontSize={22}
              color="var(--attendancies-right-view-card-container-font)"
              data-testid="label-remaining-days-2"
            />
            <StyledLabel
              children={usedSickLeaveDays == 1 ? "day" : "days"}
              boldness={300}
              fontSize={14}
              color="var(--attendancies-right-view-card-container-font)"
              data-testid="label-days-2"
            />
            <StyledLabel
              children="used"
              boldness={400}
              fontSize={16}
              color="var(--attendancies-right-view-card-container-font)"
              data-testid="label-used"
            />
            <StyledLabel
              children="Sick Leave"
              boldness={400}
              fontSize={16}
              color="var(--attendancies-right-view-card-container-font)"
              data-testid="label-sick-leave"
            />
            <StyledLabel
              children={"in"}
              boldness={400}
              fontSize={16}
              color="var(--attendancies-right-view-card-container-font)"
              data-testid="label-in"
            />
            <StyledLabel
              children={`${selectedYear}`}
              boldness={400}
              fontSize={16}
              color="var(--attendancies-right-view-card-container-font)"
              data-testid="label-year"
            />
          </CardContainer>
        </MainCardContainer>
      </TopContainer>
      <ButtonContainer data-testid="button-container">
        <StyledButton
          label="Confirm"
          onClick={handleAddAbsence}
          data-testid="button-confirm"
        />
      </ButtonContainer>
      <AbsencesPageEmployeesList
        selectedPayroll={selectedPayroll}
        selectedEmployee={selectedEmployee}
        onSelectEmployee={onSelectEmployee}
        data-testid="absences-page-employees-list"
      />
    </>
  );
};
