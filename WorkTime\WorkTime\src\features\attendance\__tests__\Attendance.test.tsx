import { render, waitFor } from "@testing-library/react";
import { MemoryRouter, Route, Routes } from "react-router-dom";
import Attendance from "../Attendance";

jest.mock("../AttendancesRightView", () => () => <div data-testid="right" />);
jest.mock("../DatesTableContainer", () => () => <div data-testid="left" />);

jest.mock("../../UserEmployeeContext", () => ({
  useUserEmployee: () => ({
    userEmployee: {
      payrolls: [],
      permissions: [],
    },
  }),
}));

describe("Attendance removes fromNotification from URL", () => {
  test("fromNotification param is removed after mount", async () => {
    const initialUrl =
      "/company-1/attendance?date=08.2025&absenceId=abc&fromNotification=true";
    render(
      <MemoryRouter initialEntries={[initialUrl]}>
        <Routes>
          <Route path=":companyId/attendance" element={<Attendance />} />
        </Routes>
      </MemoryRouter>
    );

    await waitFor(() => {
      expect(window.location.search).not.toContain("fromNotification=true");
    });
  });
});
