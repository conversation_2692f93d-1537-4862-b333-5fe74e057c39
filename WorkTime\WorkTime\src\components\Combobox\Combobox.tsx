import React, { useEffect, useRef, useState } from "react";
import styled from "styled-components";
import Image from "../Image";
import Arrow from "../../assets/images/arrows/down-filled-triangular-arrow.png";
import Label from "../Inputs/Label";
import Container from "../Container";

const OutsideContainer = styled.div<{ $isHidden: boolean }>`
  position: relative;
  min-width: 15rem;
  opacity: ${({ $isHidden }) => ($isHidden ? "0" : "1")};
  transition: 0.3s ease-in-out opacity;
  z-index: 1;
`;

const Rows = styled(Container)<{
  $isOpen: boolean;
  $height?: number;
  $disabled?: boolean;
}>`
  box-sizing: border-box;
  position: relative;
  width: 100%;
  height: ${({ $height, $isOpen }) =>
    $height && !$isOpen ? `${$height}rem` : "3rem"};
  margin-top: 0.08rem;
  background-color: ${({ $disabled }) =>
    $disabled
      ? "var(--input-field-background-color-disabled, #f5f5f5)"
      : "var(--combobox-background-color)"};

  border-radius: ${({ $isOpen }) => ($isOpen ? "2rem" : "2rem")};

  transition: border-radius 0.5s;
  z-index: 1000;
  opacity: ${({ $disabled }) => ($disabled ? 0.6 : 1)};
`;

const StyledLabel = styled(Label)<{
  $isPlaceholder: boolean;
  $isOpen?: boolean;
  $disabled?: boolean;
}>`
  padding-left: 1rem;
  padding-right: 1rem;
  font-size: 1.1rem;
  white-space: nowrap;
  display: block;
  color: ${({ $isPlaceholder, $disabled }) =>
    $disabled
      ? "var(--textbox-label-disabled-color, #999)"
      : $isPlaceholder
      ? "var(--textbox-label-blur-color)"
      : "var(--combobox-text-color)"};
  overflow-x: ${({ $isOpen }) => ($isOpen ? "visible" : "hidden")};
  text-overflow: ${({ $isOpen }) => ($isOpen ? "unset" : "ellipsis")};
  cursor: ${({ $disabled }) => ($disabled ? "not-allowed" : "pointer")};
  opacity: ${({ $disabled }) => ($disabled ? 0.6 : 1)};
`;

const ArrowImage = styled(Image)<{ $isOpen: boolean }>`
  position: absolute;
  top: 40%;
  right: 5%;
  z-index: 2;
  width: 0.8rem;
  height: 0.8rem;
  transform: ${({ $isOpen }) => ($isOpen ? "rotate(180deg)" : "rotate(0deg)")};
  transition: transform 0.3s ease;
`;

const Rectangle = styled.div<{
  $chosen: boolean;
  $isOpen: boolean;
  $index: number;
  $isLast: boolean;
  $disabled?: boolean;
}>`
  box-sizing: border-box;
  display: flex;
  position: absolute;
  width: 100%;
  height: 100%;
  background-color: ${({ $disabled }) =>
    $disabled
      ? "var(--input-field-background-color-disabled, #f5f5f5)"
      : "var(--combobox-background-color)"};
  z-index: ${({ $chosen }) => ($chosen ? 2 : 1)};
  top: ${({ $isOpen, $chosen, $index }) =>
    $isOpen && !$chosen ? `calc( 2.3rem * ${$index})` : "0"};
  border-radius: ${({ $isOpen, $chosen, $isLast }) =>
    $isOpen && !$chosen ? ($isLast ? "0rem 0rem 2rem 2rem" : "0rem") : "2rem"};
  transition: top 0.5s, border-radius 0.4s;

  justify-content: left;
  align-items: center;
  cursor: ${({ $disabled }) => ($disabled ? "not-allowed" : "pointer")};
`;

interface ComboboxProps {
  options: string[];
  height?: number;
  onChange: (chosenOption: string) => void;
  isHidden?: boolean;
  placeholder?: string;
  placeholderWhenOpen?: string;
  initialSelectedItem?: string | null;
  disabled?: boolean;
}

const Combobox: React.FC<ComboboxProps> = ({
  options,
  onChange,
  isHidden,
  placeholder,
  placeholderWhenOpen,
  height,
  initialSelectedItem,
  disabled,
}) => {
  const [currentOptions, setCurrentOptions] = useState<string[]>(
    placeholder ? [placeholder, ...options] : options
  );

  // Determine initial chosen option
  const getInitialChosenOption = () => {
    if (initialSelectedItem && options.includes(initialSelectedItem)) {
      return initialSelectedItem;
    }
    return placeholder ?? options[0];
  };

  const [chosenOption, setChosenOption] = useState(getInitialChosenOption());
  const [isOpen, setIsOpen] = useState(false);
  const comboboxRef = useRef<HTMLDivElement | null>(null);

  useEffect(() => {
    setCurrentOptions(placeholder ? [placeholder, ...options] : options);
    setChosenOption(getInitialChosenOption());
  }, [options, initialSelectedItem, placeholder]);

  const handleRectClick = (option: string, event: React.MouseEvent) => {
    event.preventDefault();
    event.stopPropagation();

    if (disabled) {
      return;
    }

    if (option === chosenOption) {
      setIsOpen(!isOpen);
    } else {
      setChosenOption(option);
      setIsOpen(false);
      onChange(option);
    }
  };

  const handleClickOutside = (event: MouseEvent) => {
    if (
      comboboxRef.current &&
      !comboboxRef.current.contains(event.target as Node)
    ) {
      setIsOpen(false);
    }
  };

  useEffect(() => {
    document.addEventListener("click", handleClickOutside);
    return () => {
      document.removeEventListener("click", handleClickOutside);
    };
  }, []);

  return (
    <OutsideContainer
      data-testid="combobox-container"
      $isHidden={isHidden ?? false}
      ref={comboboxRef}
    >
      <Rows
        data-testid="combobox-rows"
        className="combobox-rows"
        $isOpen={isOpen}
        $height={height}
        $disabled={disabled}
      >
        {isOpen && placeholderWhenOpen !== undefined && (
          <Rectangle
            data-testid="combobox-header"
            $index={0}
            key="combobox-header"
            $chosen={false}
            $isOpen={isOpen}
            $isLast={false}
            style={{
              cursor: "default",
              borderRadius: "2rem 2rem 0rem 0rem",
              background: "var(--combobox-background-color)",
              color: "var(--textbox-label-blur-color)",
            }}
            onClick={(e) => {
              e.stopPropagation();
              setIsOpen(false);
            }}
          >
            <StyledLabel
              $isPlaceholder={false}
              $isOpen={isOpen || chosenOption}
              $disabled={disabled}
            >
              {placeholderWhenOpen}
            </StyledLabel>
          </Rectangle>
        )}
        {currentOptions.map((option, index) => (
          <Rectangle
            data-testid={`combobox-option-${index}`}
            $index={index + 1}
            key={option}
            $chosen={!isOpen && chosenOption === option}
            $isOpen={isOpen}
            $isLast={index === currentOptions.length - 1}
            $disabled={disabled}
            onClick={(e) => {
              handleRectClick(option, e);
            }}
          >
            <StyledLabel
              data-testid={`combobox-label-${index}`}
              $isPlaceholder={option === placeholder}
              $isOpen={isOpen}
              $disabled={disabled}
            >
              {option}
            </StyledLabel>
          </Rectangle>
        ))}
        <ArrowImage
          data-testid="combobox-arrow"
          src={Arrow}
          alt="Arrow"
          $isOpen={isOpen}
        />
      </Rows>
    </OutsideContainer>
  );
};

export default Combobox;
