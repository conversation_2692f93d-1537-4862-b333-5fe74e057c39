import { AbsenceHospitalDTO } from "../absence/AbsenceHospitalDTO";
import { LightEmployeeDTO } from "../employees/LightEmployeeDTO";
import { NomenclatureDTO } from "../nomenclatures/NomenclatureDTO";
import { PayrollDTO } from "./PayrollDTO";

// Тук е важно да няма данни за заплати и по-конфиденциални данни,
// защото се зареждат и от служители без права.
export interface LightPayrollDTO {
  id: string;
  employee: LightEmployeeDTO;
  companyId: string;
  contractNumber: string;
  position: NomenclatureDTO;
  structureLevelId: string;
  contractType: number;
  annualPaidLeave: number;
  additionalAnnualPaidLeave: number;
  annualPaidLeavePastYears: number;
  leaves: AbsenceHospitalDTO[];
  workTimeId: string; // Това Id трябва да е равно на id, за да работи коректно
}

export const createLightPayrollDTO = (
  data: Omit<LightPayrollDTO, "workTimeId">
): LightPayrollDTO => {
  return {
    ...data,
    workTimeId: data.id,
  };
};

export const mapPayrollToLightPayroll = (
  payroll: PayrollDTO
): LightPayrollDTO => {
  return {
    id: payroll.payrollId,
    employee: payroll.employee as unknown as LightEmployeeDTO,
    companyId: payroll.companyId,
    contractNumber: payroll.contractNumber,
    position: payroll.position,
    structureLevelId: payroll.structureLevelId,
    contractType: payroll.contractType,
    annualPaidLeave: payroll.annualPaidLeave,
    additionalAnnualPaidLeave: payroll.additionalAnnualPaidLeave,
    annualPaidLeavePastYears: payroll.annualPaidLeavePastYears,
    leaves: payroll.leaves,
    workTimeId: payroll.workTimeId,
  };
};
