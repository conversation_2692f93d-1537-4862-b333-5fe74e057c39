import {
  render,
  screen,
  waitFor,
  fireEvent,
  cleanup,
} from "@testing-library/react";
import EmployeeAbsencesSideMenu from "../EmployeeAbsencesSideMenu";
import { AbsenceStatus } from "../../../models/DTOs/absence/AbsenceStatus";
import { EventType } from "../../../models/DTOs/absence/EventType";

const openModalMock = jest.fn();
const closeMenuMock = jest.fn();

jest.mock("../../../components/PopUp/ActionModalContext", () => ({
  useModal: () => ({ openModal: openModalMock }),
}));

jest.mock("../../MenuContext", () => ({
  useMenu: () => ({
    toggleMenu: jest.fn(),
    closeMenu: closeMenuMock,
    isOpen: true,
    viewData: {
      selectedPayroll: { workTimeId: "worktime-1" },
      selectedMonth: 8,
      selectedYear: 2025,
    },
  }),
}));

jest.mock("../../companies/CompanyContext", () => ({
  useCompany: () => ({ company: { id: "company-1", name: "Acme" } }),
}));

jest.mock("../../authentication/AuthContext", () => ({
  useAuth: () => ({ user: { userId: "user-1" } }),
}));

jest.mock("../../UserEmployeeContext", () => ({
  useUserEmployee: () => ({
    userEmployee: { permissions: [], payrolls: [{ id: "p1" }] },
  }),
}));

jest.mock("../../../app/hooks", () => ({
  useAppDispatch: () => jest.fn(),
  useAppSelector: () => ({ payrolls: [] }),
}));

jest.mock("../../payroll/payrollsActions", () => ({
  onPayrollsLoaded: jest.fn(),
  onPayrollAbsenceUpdated: jest.fn(),
  onPayrollAbsenceRemoved: jest.fn(),
  selectPayrolls: () => ({ payrolls: [] }),
}));

jest.mock("../components/AbsenceButtons", () => () => (
  <div data-testid="mock-absence-buttons" />
));
jest.mock("../components/AbsenceList", () => () => (
  <div data-testid="mock-absence-list" />
));
jest.mock("../components/AbsenceDetails", () => () => (
  <div data-testid="mock-absence-details" />
));

jest.mock("../../../components/Datepicker/Datepicker", () => {
  return ({
    label,
    onSelectDate,
  }: {
    label: string;
    onSelectDate: (d: Date) => void;
  }) => {
    if (label === "Start Date") {
      onSelectDate(new Date(2025, 7, 11));
    } else if (label === "End Date") {
      onSelectDate(new Date(2025, 7, 14));
    }
    return <div data-testid={`mock-datepicker-${label}`} />;
  };
});

jest.mock("../../../components/Combobox/Combobox", () => (props: any) => (
  <div data-testid="mock-combobox">{props.initialSelectedItem}</div>
));
jest.mock("../../../components/Inputs/Textbox", () => (props: any) => (
  <input
    data-testid="mock-textbox"
    value={props.value}
    onChange={(e) => props.handleChange(e)}
  />
));

const mockPost = jest.fn();
jest.mock("../../../services/worktimeConnectionService", () => ({
  authenticatedPost: (...args: any[]) => mockPost(...args),
  authenticatedPut: jest.fn(),
}));

const buildAbsence = () => ({
  id: "a1",
  payrollId: "worktime-1",
  type: "",
  typeIdentifier: EventType.ПлатенГодишенОтпуск,
  fromDate: new Date(2025, 7, 11).toISOString(),
  toDate: new Date(2025, 7, 14).toISOString(),
  duration: 3,
  status: AbsenceStatus.Pending,
  isHospital: false,
  isOverlapping: false,
  exportStatus: 0,
});

afterEach(() => {
  cleanup();
  jest.clearAllMocks();
});

describe("EmployeeAbsencesSideMenu - closing the menu on create", () => {
  test("does not close the menu when absence is created without warnings", async () => {
    mockPost.mockResolvedValueOnce({
      value: [buildAbsence()],
      isSuccess: true,
      isWarning: false,
      isError: false,
      validationError: [],
      validationWarning: {
        validationErrors: [],
        hasValidationErrors: false,
        validationWarnings: [],
        hasValidationWarnings: false,
      },
    });

    render(<EmployeeAbsencesSideMenu />);

    const requestBtn = await screen.findByTestId("request-button");
    fireEvent.click(requestBtn);

    await new Promise((r) => setTimeout(r, 50));
    expect(closeMenuMock).not.toHaveBeenCalled();
    expect(openModalMock).not.toHaveBeenCalled();
  });

  test("closes the menu after user confirms when there is an overlap warning", async () => {
    mockPost.mockResolvedValueOnce({
      value: [buildAbsence()],
      isSuccess: true,
      isWarning: true,
      isError: false,
      validationError: [],
      validationWarning: {
        validationErrors: [],
        hasValidationErrors: false,
        validationWarnings: [30001],
        hasValidationWarnings: true,
      },
    });

    render(<EmployeeAbsencesSideMenu />);

    const requestBtn = await screen.findByTestId("request-button");
    fireEvent.click(requestBtn);

    await waitFor(() => expect(openModalMock).toHaveBeenCalledTimes(1));
    expect(closeMenuMock).not.toHaveBeenCalled();

    const modalArgs = openModalMock.mock.calls[0]?.[0];
    modalArgs.onConfirm?.();

    await waitFor(() => expect(closeMenuMock).toHaveBeenCalledTimes(1));
  });
});
