import { AbsenceExportStatus } from "./AbsenceExportStatus";
import { AbsenceStatus } from "./AbsenceStatus";
import { EventType } from "./EventType";

export interface AbsenceHospitalDTO {
  id?: string;
  payrollId: string;
  type: string;
  typeIdentifier: EventType;
  reference?: string;
  sickNote?: string;
  fromDate: string;
  toDate: string;
  duration: number;
  status: AbsenceStatus;
  isHospital: boolean;
  isOverlapping: boolean;
  exportStatus: AbsenceExportStatus;
}
