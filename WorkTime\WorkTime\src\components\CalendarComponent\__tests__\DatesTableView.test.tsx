import { render, screen, fireEvent } from "@testing-library/react";
import DatesTableView, {
  DayInfo,
} from "../../CalendarComponent/DatesTableView";

jest.mock("../../CalendarComponent/CalendarHeader", () => ({
  CalendarHeader: ({ goToPrevMonth, goToNextMonth }: any) => (
    <div>
      <button onClick={goToPrevMonth} data-testid="prev-month" />
      <button onClick={goToNextMonth} data-testid="next-month" />
    </div>
  ),
}));

jest.mock("../../CalendarComponent/DayCell", () => ({
  DayCell: ({ "data-testid": testId }: any) => <div data-testid={testId} />,
}));

describe("DatesTableView", () => {
  const baseProps = {
    selectedMonth: 7,
    selectedYear: 2025,
    usedLeavesThisMonth: 2,
    usedHospitalLeavesThisMonth: 1,
    days: Array.from({ length: 7 }, (_, i) => ({
      dayDate: new Date(2025, 7, i + 1),
      dayNumber: i + 1,
      type: "currentMonth",
      missingEmployees: [],
    })) as DayInfo[],
    maxRowsPerWeek: { 0: 3 },
    isCurrentDay: () => false,
    showMyAbsences: false,
    setSelectedPayroll: () => {},
  } as any;

  test("renders days and handles month navigation", () => {
    const onPrevMonth = jest.fn();
    const onNextMonth = jest.fn();
    render(
      <DatesTableView
        {...baseProps}
        onPrevMonth={onPrevMonth}
        onNextMonth={onNextMonth}
      />
    );
    expect(screen.getAllByTestId(/day-cell-/).length).toBe(7);
    fireEvent.click(screen.getByTestId("prev-month"));
    fireEvent.click(screen.getByTestId("next-month"));
    expect(onPrevMonth).toHaveBeenCalledTimes(1);
    expect(onNextMonth).toHaveBeenCalledTimes(1);
  });
});
