import { Ref, forwardRef, useEffect, useState } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import styled from "styled-components";
import { useAppDispatch } from "../app/hooks";
import { onClearState } from "../app/store";
import logoutImg from "../assets/images/menu/logout.png";
import ArrowTip from "../components/ArrowTip";
import { UserEmployeeDTO } from "../models/DTOs/users/UserEmployeeDTO";
import { logout } from "../services/authentication/authenticationService";
import { initCompany } from "../services/companies/companiesService";
import Translator from "../services/language/Translator";
import { initUserEmployee } from "../services/users/userService";
import { useMenu } from "./MenuContext";
import { useUserEmployee } from "./UserEmployeeContext";
import { useModal } from "../components/PopUp/ActionModalContext";
import EmployeeAbsencesSideMenu from "./absences/EmployeeAbsencesSideMenu";
import { useAuth } from "./authentication/AuthContext";
import { useCompany } from "./companies/CompanyContext";
import EditCompany from "./companies/EditCompany";
import ProfileSideMenu from "./employees/employeeProfile/ProfileSideMenu";
import NewEmployeeSideMenu from "./employees/newEmployee/NewEmployeeSideMenu";
import EditEmployeeDataSideMenu from "./employees/editEmployee/EditEmployeeDataSideMenu";
import EditEmployeeAddressesSideMenu from "./employees/editEmployee/EditEmployeeAddressesSideMenu";
import { useAbsence } from "./absences/AbsenceContext";
import Label from "../components/Inputs/Label";
import AddAddresses from "./employees/employeeProfile/AddAddresses";

const StyledMenu = styled.div<{ $isOpen: boolean }>`
  position: fixed;
  right: 0;
  top: 0;
  height: 100%;
  width: 30rem;
  background: var(--menu-background-color);
  box-shadow: 0 3px 10px rgb(0 0 0 / 0.2);
  transform: ${({ $isOpen }) =>
    $isOpen ? "translateX(0)" : "translateX(100%)"};
  transition: transform 0.4s ease-in;
  z-index: 1001;
  @media (max-width: 800px) {
    width: 13rem;
  }
`;

const NavContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
  top: 0;
  width: 100%;
  margin: 0;
  background-color: var(--menu-header-background-color);
  padding: 1rem 0.6rem 1rem 0.6rem;
  box-sizing: border-box;
  height: 5rem;
`;

const LogOutImage = styled.img`
  width: 1.5rem;
  height: 1.5rem;
  cursor: pointer;
`;

const StyledGrid = styled.div`
  display: grid;

  @media (max-width: 800px) {
    grid-template-columns: 1fr;
    grid-template-rows: repeat(4, 1fr);
  }
`;

const ContentContainer = styled.div`
  display: flex;
  flex-direction: column;
  padding: 1em;
  box-sizing: border-box;
  height: 93%;
  overflow-y: auto;
`;

const UserContainer = styled.div`
  color: var(--user-container-color);
  margin-right: 3rem;
`;

const NewEmployeeContainer = styled.div`
  color: var(--user-container-color);
  margin-left: 1rem;
  margin-right: auto;
`;

const MyCompaniesContainer = styled.div`
  color: var(--companies-container-color);
  margin-right: 4rem;
  font-size: 1.3rem;
  margin-left: -1rem;
`;

const CompanyName = styled.div`
  font-size: 1rem;
  display: flex;
  justify-content: center;
  text-align: center;
  margin-right: 2rem;
  margin-left: 2rem;
`;

const AbsenceHeaderWrapper = styled.div`
  position: absolute;
  left: 3rem;
  transform: none;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
`;

const EmployeeNameHeader = styled.div`
  color: var(--user-container-color);
  font-size: 1.5rem;
`;

const AbsenceInfo = styled(Label)`
  color: var(--absence-info-side-menu-header);
  font-size: 1.125rem;
  text-align: center;
`;

interface MenuProps {
  isOpen: boolean;
  onToggleMenu: () => void;
}

const Menu = forwardRef<HTMLDivElement, MenuProps>(
  ({ isOpen }, ref: Ref<HTMLDivElement>) => {
    const { currentPage, closeMenu, activeView, menuOpenedFrom } = useMenu();
    const { isModalOpen } = useModal();

    const { company, setCompany, resetCompany } = useCompany();
    const { setUserEmployee, resetUserEmployee } = useUserEmployee();
    const { selectedAbsence, isEditing } = useAbsence();

    const navigate = useNavigate();

    const { user, resetUser } = useAuth();

    const companyPages = ["/company-structure", "/payrolls"];
    const employeePages = ["/employees"];

    const [activeMenu, setActiveMenu] = useState<string>("");
    const [goBack, setGoBack] = useState(false);
    const location = useLocation();
    const dispatch = useAppDispatch();

    useEffect(() => {
      const initializeData = async () => {
        const companyData = await initCompany();
        setCompany(companyData);
        await getUserEmployee();
      };

      initializeData();
    }, [setCompany]);

    const getUserEmployee = async () => {
      await initUserEmployee().then((response: UserEmployeeDTO) => {
        setUserEmployee(response);
      });
    };

    const handleButtonClickFromProfile = (button: string) => {
      setActiveMenu(button);
      setGoBack(false);
    };

    const handleArrowClick = () => {
      if (activeMenu === "myCompanies") {
        setGoBack(true);
      } else if (!isModalOpen) {
        closeMenu();
      }
    };

    const handleLogOutClick = () => {
      handleLogout();
    };

    const handleLogout = () => {
      try {
        logout();
        dispatch(onClearState());
      } finally {
        resetUser();
        resetUserEmployee();
        resetCompany();
        navigate("/auth/login");
        closeMenu();
      }
    };

    const getAbsenceHeaderText = () => {
      if (!selectedAbsence) return "Absence request";
      if (isEditing) {
        return "Edit absence";
      }
      return "strAbsenceInfo";
    };

    return (
      <StyledMenu $isOpen={isOpen} ref={ref} data-testid="menu">
        <NavContainer data-testid="menu-nav-container">
          <ArrowTip
            direction={activeMenu === "myCompanies" ? "left" : "right"}
            onClick={handleArrowClick}
            data-testid="menu-arrow-tip"
          />
          {menuOpenedFrom === "companyLabel" ? (
            location.pathname !== "/" ? (
              <CompanyName data-testid="menu-company-name">
                {company?.name}
              </CompanyName>
            ) : (
              <></>
            )
          ) : (
            <>
              {activeView === "absence" ? (
                <AbsenceHeaderWrapper>
                  {selectedAbsence && (
                    <EmployeeNameHeader>
                      {selectedAbsence.employeeName}
                    </EmployeeNameHeader>
                  )}
                  <AbsenceInfo data-testid="menu-absence-title">
                    {getAbsenceHeaderText()}
                  </AbsenceInfo>
                </AbsenceHeaderWrapper>
              ) : activeView === "new-employee" ? (
                <NewEmployeeContainer>
                  <Translator
                    getString="New employee"
                    data-testid="menu-new-employee-title"
                  />
                </NewEmployeeContainer>
              ) : activeView === "edit-employee" ? (
                <NewEmployeeContainer>
                  <Translator
                    getString="Edit employee"
                    data-testid="menu-edit-employee-title"
                  />
                </NewEmployeeContainer>
              ) : employeePages.some((p) => currentPage.includes(p)) ? (
                <UserContainer data-testid="menu-user-container">
                  {activeMenu !== "myCompanies" ? (
                    <>
                      <span data-testid="menu-user-name">
                        {user?.firstName} {user?.secondName} {user?.lastName}
                      </span>
                      <br />
                      <span data-testid="menu-user-email">{user?.email}</span>
                    </>
                  ) : (
                    <MyCompaniesContainer data-testid="menu-companies-container">
                      <Translator
                        getString="My Companies"
                        data-testid="menu-companies-title"
                      />
                    </MyCompaniesContainer>
                  )}
                </UserContainer>
              ) : location.pathname !== "/" ? (
                <CompanyName data-testid="menu-company-name">
                  {company?.name}
                </CompanyName>
              ) : (
                <></>
              )}
            </>
          )}

          {activeView === "profile" && (
            <LogOutImage
              onClick={handleLogOutClick}
              src={logoutImg}
              data-testid="menu-logout-button"
            />
          )}
        </NavContainer>
        <ContentContainer data-testid="menu-content-container">
          <StyledGrid data-testid="menu-grid">
            {menuOpenedFrom === "companyLabel" ? (
              <EditCompany data-testid="menu-edit-company" />
            ) : activeView === "new-employee" ? (
              <NewEmployeeSideMenu data-testid="new-employee-side-menu" />
            ) : activeView === "new-address" ? (
              <AddAddresses data-testid="new-address-side-menu" />
            ) : activeView === "edit-employee" ? (
              <EditEmployeeDataSideMenu data-testid="edit-employee-side-menu" />
            ) : activeView === "edit-employee-addresses" ? (
              <EditEmployeeAddressesSideMenu data-testid="edit-employee-addresses-side-menu" />
            ) : activeView === "absence" ? (
              <EmployeeAbsencesSideMenu data-testid="menu-absence" />
            ) : company?.id !== "" &&
              companyPages.some((p) => p === currentPage) ? (
              <EditCompany data-testid="menu-edit-company" />
            ) : (
              <ProfileSideMenu
                onButtonClick={handleButtonClickFromProfile}
                goBack={goBack}
                initialPage={
                  menuOpenedFrom === "associates" ? "coworkers" : "profile"
                }
                data-testid="menu-profile-side-menu"
              />
            )}
          </StyledGrid>
        </ContentContainer>
      </StyledMenu>
    );
  }
);

export default Menu;
