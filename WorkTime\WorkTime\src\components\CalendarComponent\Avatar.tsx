import styled from "styled-components";
import { AbsenceStatus } from "../../models/DTOs/absence/AbsenceStatus";

interface AvatarProps
  extends React.DetailedHTMLProps<
    React.AllHTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  photo: string;
  name: string;
  size: number;
  isVisible: boolean;
  background: string;
}

const getStringHashCode = (name: string) => {
  let initials: string;
  const nameSplit = name.split(" ");
  const nameLength = nameSplit.length;
  if (nameLength > 1) {
    initials =
      nameSplit[0].substring(0, 1) + nameSplit[nameLength - 1].substring(0, 1);
  } else if (nameLength === 1) {
    initials = nameSplit[0].substring(0, 1);
  } else return;

  return initials.toUpperCase();
};

export const getAbsenceAvatarColor = (
  status: AbsenceStatus,
  isHospital: boolean
) => {
  if (
    status === AbsenceStatus.Pending ||
    status === AbsenceStatus.EditedByEmployee ||
    status === AbsenceStatus.DeletedByUserAfterApproval
  )
    return "#EB7B99";
  if (isHospital) return "#268DDC";
  return "#6AC97D";
};

const AvatarDiv = styled.div<{
  $initials: string;
  $background: string;
  $isVisible: boolean;
}>`
  display: ${(props) => (props.$isVisible ? "block" : "none")};
  position: relative;
  overflow: hidden;
  color: var(--avatar-initials-color);
  text-align: center;
  background: ${(p) => p.$background};
  border-radius: 50%;
  height: 100%;
  width: 1.6em;
`;

const AvatarPhotoContainer = styled.div<{
  $size: number;
  $isVisible: boolean;
  $background: string;
}>`
  background: ${(p) => p.$background};
  ${({ $size }) => `
       height: ${$size}rem;
       width: ${$size}rem;
       border-radius: ${$size / 2}rem;
  `}
  margin-left: 1rem;
  display: ${(props) => (props.$isVisible ? "flex" : "none")};
  align-items: center;
  justify-content: center;
  overflow: hidden;
  box-sizing: border-box;
`;

const AvatarPhoto = styled.img<{
  $size: number;
}>`
  ${({ $size }) => `
       height: ${$size * 0.55}rem;
       width: ${$size * 0.55}rem;
  `}
  object-fit: contain;
`;

const Avatar = (props: AvatarProps) => {
  const { photo, name, size, isVisible, background } = props;
  const initials = getStringHashCode(name) ?? "";
  return (
    <>
      {photo.length === 0 ? (
        <AvatarDiv
          $background={background}
          $initials={initials}
          $isVisible={isVisible}
          data-testid="avatar-initials"
        >
          {photo} {initials}
        </AvatarDiv>
      ) : (
        <AvatarPhotoContainer
          style={{ ...props.style }}
          $background={background}
          $isVisible={isVisible}
          $size={size}
          data-testid="avatar-photo"
        >
          <AvatarPhoto src={photo} $size={size} />
        </AvatarPhotoContainer>
      )}
    </>
  );
};

export default Avatar;
