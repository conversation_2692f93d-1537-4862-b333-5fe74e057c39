import { HeaderType } from "../models/Enums/HeaderType";
import Translator from "../services/language/Translator";

interface HeaderProps
  extends React.DetailedHTMLProps<
    React.AllHTMLAttributes<HTMLHeadingElement>,
    HTMLHeadingElement
  > {
  headerType?: HeaderType;
  content: string;
}

export const Header = (props: HeaderProps) => {
  const { content, headerType, ...rest } = props;

  switch (headerType) {
    case HeaderType.H1:
    default:
      return (
        <h1 {...rest} data-testid="header-h1">
          {<Translator getString={content} />}
        </h1>
      );
    case HeaderType.H2:
      return <h2 {...rest}>{<Translator getString={content} />}</h2>;
    case HeaderType.H3:
      return (
        <h3 {...rest} data-testid="header-h3">
          {<Translator getString={content} />}
        </h3>
      );
    case HeaderType.H4:
      return (
        <h4 {...rest} data-testid="header-h4">
          {<Translator getString={content} />}
        </h4>
      );
    case HeaderType.H5:
      return (
        <h5 {...rest} data-testid="header-h5">
          {<Translator getString={content} />}
        </h5>
      );
    case HeaderType.H6:
      return (
        <h6 {...rest} data-testid="header-h6">
          {<Translator getString={content} />}
        </h6>
      );
  }
};
