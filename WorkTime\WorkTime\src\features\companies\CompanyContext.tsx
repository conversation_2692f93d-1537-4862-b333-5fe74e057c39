import { createContext, useContext, useEffect, useState } from "react";
import { CompanyDTO } from "../../models/DTOs/companies/CompanyDTO";
import {
  initCompany,
  setCompanyLocalStorage,
} from "../../services/companies/companiesService";
import { onPayrollsLoaded } from "../payroll/payrollsActions";
import { useAppDispatch, useAppSelector } from "../../app/hooks";
import { companiesState } from "./companiesActions";
import { useLocation } from "react-router-dom";

const initialCompanyState: CompanyDTO = {
  id: "",
  name: "",
  bulstat: "",
  userRegistrationCompanyId: 0,
  contactName: "",
};

interface CompanyContextType {
  company: CompanyDTO;
  setCompany: (company: CompanyDTO) => void;
  resetCompany: () => void;
}

const CompanyContext = createContext<CompanyContextType | undefined>(undefined);

export const CompanyProvider = ({
  children,
}: {
  children: React.ReactNode;
}) => {
  const dispatch = useAppDispatch();
  const [company, setCompany] = useState<CompanyDTO>(initialCompanyState);
  const { activeCompanies } = useAppSelector(companiesState);
  const location = useLocation();

  const resetCompany = () => {
    setCompany(initialCompanyState);
  };

  useEffect(() => {
    const initializeCompany = async () => {
      const companyData = await initCompany();
      setCompany(companyData);

      dispatch(onPayrollsLoaded(companyData.id));
    };

    initializeCompany();
  }, [setCompany]);

  useEffect(() => {
    const firstSegment = location.pathname.split("/")[1];
    if (!firstSegment) return;

    if (firstSegment !== company.id) {
      const updatedCompany = { ...company, id: firstSegment };
      setCompany(updatedCompany);
      setCompanyLocalStorage(updatedCompany);
      dispatch(onPayrollsLoaded(firstSegment));
    }

    const companyFromState = activeCompanies?.find(
      (c) => c.id === firstSegment
    );
    if (companyFromState && companyFromState.id !== company.id) {
      setCompany(companyFromState);
      setCompanyLocalStorage(companyFromState);
      dispatch(onPayrollsLoaded(companyFromState.id));
    }
  }, [location.pathname, activeCompanies, company.id]);

  return (
    <CompanyContext.Provider value={{ company, setCompany, resetCompany }}>
      {children}
    </CompanyContext.Provider>
  );
};

export const useCompany = (): CompanyContextType => {
  const context = useContext(CompanyContext);
  if (context === undefined) {
    throw new Error("useCompany must be used within a CompanyProvider");
  }
  return context;
};
