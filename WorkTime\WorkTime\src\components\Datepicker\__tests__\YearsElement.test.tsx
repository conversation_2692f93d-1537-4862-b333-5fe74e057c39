import { render, fireEvent, screen } from "@testing-library/react";
import YearsElement from "../YearsElement";
import { ViewMode } from "../../../models/Enums/ViewMode";
import "@testing-library/jest-dom";

describe("<YearsElement />", () => {
  it("should display years if active prop is YearsView", () => {
    render(
      <YearsElement
        handleYearsClick={jest.fn()}
        selectedGroupIndex={0}
        active={ViewMode.YearsView}
      />
    );
    const yearContainer = screen.getByTestId("years-grid");
    expect(yearContainer).toBeInTheDocument();
  });

  it("should call handleYearsClick with correct year when a year is clicked", () => {
    const mockHandleYearsClick = jest.fn();
    render(
      <YearsElement
        handleYearsClick={mockHandleYearsClick}
        selectedGroupIndex={0}
        active={ViewMode.YearsView}
      />
    );

    const year = screen.getByText("1900");
    fireEvent.click(year);
    expect(mockHandleYearsClick).toHaveBeenCalledWith(1900);
  });

  it("should display the correct group of years based on selectedGroupIndex", () => {
    render(
      <YearsElement
        handleYearsClick={jest.fn()}
        selectedGroupIndex={1}
        active={ViewMode.YearsView}
      />
    );

    expect(screen.getByText("1912")).toBeInTheDocument();
    expect(screen.getByText("1923")).toBeInTheDocument();

    expect(screen.queryByText("1900")).not.toBeInTheDocument();
    expect(screen.queryByText("1924")).not.toBeInTheDocument();
  });
});
