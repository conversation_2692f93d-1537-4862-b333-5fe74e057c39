import { render, screen, fireEvent, waitFor } from "@testing-library/react";
import AbsenceForm from "../AbsenceForm";
import { AbsenceStatus } from "../../../../models/DTOs/absence/AbsenceStatus";
import { EventType } from "../../../../models/DTOs/absence/EventType";

jest.mock("../../../../utils/absenceActions", () => ({
  useAbsenceActions: () => ({
    handleDeleteAbsence: jest.fn(),
    handleApproveAbsence: jest.fn(),
    handleDeclineAbsence: jest.fn(),
  }),
}));

jest.mock("../../../MenuContext", () => ({
  useMenu: () => ({ closeMenu: jest.fn() }),
}));

const baseSelectedAbsence = {
  id: "leave-emp-1",
  payrollId: "worktime-1",
  userId: "user-1",
  employeeName: "<PERSON>",
  row: 1,
  positonRounding: 1,
  isHospital: false,
  status: AbsenceStatus.Pending,
  isHighlighted: false,
  startDate: new Date(2025, 7, 1).toISOString(),
  endDate: new Date(2025, 7, 3).toISOString(),
  isOverlapping: false,
  typeIdentifier: EventType.ПлатенГодишенОтпуск as unknown as string,
  comment: "",
  sickNote: "",
  exportStatus: 0,
};

describe("AbsenceForm - edit button enabling", () => {
  test("enables Edit button when only absence type is changed by employee on pending absence", async () => {
    render(
      <AbsenceForm
        options={["Paid Leave", "Unpaid Leave"]}
        isLoading={false}
        selectedAbsence={baseSelectedAbsence as any}
        absencesVisible={true}
        onSubmit={jest.fn()}
        onEdit={jest.fn()}
        isEditing={true}
        isAdmin={false}
      />
    );

    const buttons = await screen.findAllByTestId("button-container");
    const editButton = buttons.find((b) =>
      /Редакция|Edit/i.test(b.textContent || "")
    );
    expect(editButton).toBeTruthy();
    expect(editButton).toBeDisabled();

    const secondOption = await screen.findByTestId("combobox-option-1");
    fireEvent.click(secondOption);

    await waitFor(() => {
      expect(editButton).not.toBeDisabled();
    });
  });

  test("enables Edit button when switching between paid leave and hospital (sick leave)", async () => {
    render(
      <AbsenceForm
        options={["Paid Leave", "Sick Leave"]}
        isLoading={false}
        selectedAbsence={baseSelectedAbsence as any}
        absencesVisible={true}
        onSubmit={jest.fn()}
        onEdit={jest.fn()}
        isEditing={true}
        isAdmin={false}
      />
    );

    const buttons = await screen.findAllByTestId("button-container");
    const editButton = buttons.find((b) =>
      /Редакция|Edit/i.test(b.textContent || "")
    );
    expect(editButton).toBeTruthy();
    expect(editButton).toBeDisabled();

    const secondOption = await screen.findByTestId("combobox-option-1");
    fireEvent.click(secondOption);

    await waitFor(() => {
      expect(editButton).not.toBeDisabled();
    });
  });
});
