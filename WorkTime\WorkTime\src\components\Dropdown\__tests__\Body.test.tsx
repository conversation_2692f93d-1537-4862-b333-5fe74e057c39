import { render, screen, fireEvent } from "@testing-library/react";
import { DropdownContext } from "../Context";
import { Body } from "../Body";
import "@testing-library/jest-dom";

describe("Body Component", () => {
  const mockSetIsOpen = jest.fn();

  const renderBodyWithDropdownContext = (isOpen: boolean) => {
    return render(
      <DropdownContext.Provider value={{ isOpen, setIsOpen: mockSetIsOpen }}>
        <Body>
          <div>Dropdown Content</div>
        </Body>
      </DropdownContext.Provider>
    );
  };

  it("renders dropdown content when isOpen is true", () => {
    renderBodyWithDropdownContext(true);
    expect(screen.getByText("Dropdown Content")).toBeInTheDocument();
  });

  it("does not render dropdown content when isOpen is false", () => {
    renderBodyWithDropdownContext(false);
    expect(screen.queryByText("Dropdown Content")).toBeNull();
  });

  it("calls setIsOpen with false when dropdown is clicked", () => {
    jest.useFakeTimers();
    renderBodyWithDropdownContext(true);
    fireEvent.click(screen.getByText("Dropdown Content"));
    jest.runAllTimers();
    expect(mockSetIsOpen).toHaveBeenCalledWith(false);
  });
});
