import { NomenclatureDTO } from "../nomenclatures/NomenclatureDTO";

export interface AddressDTO {
  id: string;
  employeeId: string | undefined;
  city: NomenclatureDTO | null;
  country: string;
  district: NomenclatureDTO | null;
  municipality: NomenclatureDTO | null;
  region: string;
  description: string;
  block: string;
  street: string;
  apartment: string;
  postalCode: string;
  neighborhood: string;
  phone: string;
  workPhone: string;
  email: string;
  workEmail: string;
  purpose: NomenclatureDTO;
  cityName: string;
  districtName: string;
  municipalityName: string;
}

export enum AddressPurpose {
  IdentityCard = 0,
  ForContact = 1,
  ForRemoteWork = 3,
  Abroad = 4,
  Custom = 5,
}
