import styled from "styled-components";
import Container from "../Container";
import Image from "../Image";
import Textbox from "../Inputs/Textbox";

interface DatepickerProps {
  $isDatePickerActive: boolean;
}
interface ArrowProps {
  $angle?: number;
  $left?: number;
  $top?: number;
  $right?: number;
}

export const ArrowImage = styled(Image)<ArrowProps>`
  position: absolute;
  left: ${({ $left }) => ($left ? `${$left}rem` : "auto")};
  top: ${({ $top }) => ($top ? `${$top}rem` : "auto")};
  right: ${({ $right }) => ($right ? `${$right}rem` : "auto")};
  transform: ${({ $angle }) => ($angle ? `rotate(${$angle}deg)` : "none")};
  width: 1.5rem;
  height: 1.2rem;
`;

export const LabelCentered = styled(Container)<{ $fontSize: number }>`
  font: Segoe UI;
  text-align: center;
  height: clamp(1em, 1.5vw, 1.5em);
  color: white;
  font-size: ${({ $fontSize }) => ($fontSize ? `${$fontSize}rem` : "1.3rem")};
`;

export const ContainerNavBar = styled.div`
  display: flex;
  flex-direction: column;
  position: relative;
  align-items: center;
  justify-content: center;
  font-size: 1em;
  color: var(--datepicker-header-font-color);
  width: 15em;
  height: 1em;
  padding: 1.1em 0.3em 1.1em 0.6em;

  border-radius: 1em 1em 0 0;
  background-color: var(--datepicker-header-background-color);
  cursor: pointer;

  user-select: none;
`;

export const ContainerNavButtons = styled.div`
  height: 1em;
  padding: 0.2em;
`;

export const MainContainer = styled.div<DatepickerProps>`
  display: flex;
  height: 3em;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: ${({ $isDatePickerActive }) => ($isDatePickerActive ? "4" : "3")};
  cursor: pointer;
  user-select: none;
  width: 100%;
`;

export const DatePickerIcon = styled(Image)`
  position: absolute;
  right: 0.5em;
`;

export const ContainerDropDown = styled.div<{ $active: boolean }>`
  display: none;
  position: absolute;
  top: 100%;
  right: 0;

  ${({ $active }) =>
    $active &&
    `
    display: block;
  `}
`;

export const StyledTextBox = styled(Textbox)`
  width: 100%;
`;
