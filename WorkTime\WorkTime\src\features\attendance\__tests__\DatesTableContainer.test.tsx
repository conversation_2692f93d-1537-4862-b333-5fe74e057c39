import { render, waitFor, cleanup } from "@testing-library/react";
import DatesTableContainer from "../DatesTableContainer";
import { AbsenceStatus } from "../../../models/DTOs/absence/AbsenceStatus";

const openModalMock = jest.fn();
jest.mock("../../../components/PopUp/ActionModalContext", () => ({
  useModal: () => ({ openModal: openModalMock }),
}));

jest.mock("../../MenuContext", () => ({
  useMenu: () => ({
    toggleMenu: jest.fn(),
    changeView: jest.fn(),
    isOpen: true,
  }),
}));

jest.mock("../../absences/AbsenceContext", () => ({
  useAbsence: () => ({ setSelectedAbsence: jest.fn() }),
}));

jest.mock("../../companies/CompanyContext.tsx", () => ({
  useCompany: () => ({ company: { id: "company-1" } }),
}));

jest.mock("../../../app/hooks", () => ({
  useAppDispatch: () => jest.fn(),
  useAppSelector: () => ({
    payrolls: [
      {
        id: "worktime-1",
        employee: { userId: "user-1", firstName: "John", lastName: "Doe" },
        leaves: [
          {
            id: "leave-1",
            payrollId: "worktime-1",
            fromDate: new Date(2025, 7, 1).toISOString(),
            toDate: new Date(2025, 7, 3).toISOString(),
            isHospital: false,
            status: AbsenceStatus.Approved,
            isOverlapping: false,
            typeIdentifier: "vac",
            exportStatus: 0,
            reference: "",
            sickNote: "",
          },
          {
            id: "leave-2",
            payrollId: "worktime-1",
            fromDate: new Date(2025, 7, 10).toISOString(),
            toDate: new Date(2025, 7, 10).toISOString(),
            isHospital: false,
            status: AbsenceStatus.Approved,
            isOverlapping: false,
            typeIdentifier: "vac",
            exportStatus: 0,
            reference: "",
            sickNote: "",
          },
        ],
      },
    ],
  }),
}));

jest.mock("../../../components/CalendarComponent/DatesTableView", () => () => {
  return <div data-testid="dates-table-view" />;
});

jest.mock("../../payroll/payrollsActions", () => ({
  onPayrollsLoaded: jest.fn(),
  selectPayrolls: () => ({ payrolls: [] }),
}));

afterEach(() => {
  cleanup();
  openModalMock.mockReset();
});

describe("DatesTableContainer notification modal gating", () => {
  const baseProps = {
    selectedPayroll: undefined,
    setSelectedPayroll: () => {},
    selectedEmployee: undefined,
    hoveredEmployee: undefined,
    showMyAbsences: false,
    selectedMonth: 7,
    selectedYear: 2025,
    setSelectedMonth: () => {},
    setSelectedYear: () => {},
  } as const;

  test("shows approved modal on initial render only when isFromNotification is true", async () => {
    render(
      <DatesTableContainer
        {...baseProps}
        highlightedAbsenceId="leave-1"
        isFromNotification={true}
      />
    );

    await waitFor(() => expect(openModalMock).toHaveBeenCalledTimes(1));
  });

  test("does not show modal again for subsequent selections when isFromNotification becomes false", async () => {
    const { rerender } = render(
      <DatesTableContainer
        {...baseProps}
        highlightedAbsenceId="leave-1"
        isFromNotification={true}
      />
    );

    await waitFor(() => expect(openModalMock).toHaveBeenCalledTimes(1));

    // Simulate URL param removed by parent (Attendance) and user clicking another approved absence
    rerender(
      <DatesTableContainer
        {...baseProps}
        highlightedAbsenceId="leave-2"
        isFromNotification={false}
      />
    );

    // Modal should not be called again
    await waitFor(() => expect(openModalMock).toHaveBeenCalledTimes(1));
  });

  test("does not show modal when isFromNotification is false initially", async () => {
    render(
      <DatesTableContainer
        {...baseProps}
        highlightedAbsenceId="leave-1"
        isFromNotification={false}
      />
    );

    // Give effects time to run and confirm no calls
    await new Promise((r) => setTimeout(r, 50));
    expect(openModalMock).not.toHaveBeenCalled();
  });
});
