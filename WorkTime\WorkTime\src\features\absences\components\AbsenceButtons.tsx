import { css, styled } from "styled-components";
import Container from "../../../components/Container";
import Button from "../../../components/Inputs/Button";
import planceImage from "../../../assets/images/button/plane.png";
import planceDisableImage from "../../../assets/images/button/planeDisabled.png";
import heartImage from "../../../assets/images/button/heart.png";
import heartDisableImage from "../../../assets/images/button/heartDisabled.png";

const AbsencesButtonsContainer = styled(Container)`
  display: flex;
  margin-bottom: 1rem;
`;

interface StyledButtonProps {
  $image: string;
  $hoverImage: string;
  $disabledImage: string;
  $isDisable: boolean;
}

const StyledButton = styled(Button)<StyledButtonProps>`
  width: 100%;
  background-color: var(--absence-button-background-color);
  color: var(--absence-button-color);
  font-size: 1.125rem;
  margin-left: 0.225rem;
  background-image: url(${(p) => p.$image});
  overflow: hidden;
  background-repeat: no-repeat;
  background-size: 1.6rem;
  background-position: left 1rem center;
  cursor: default;

  ${(p) => p.$isDisable && Disable}

  ${(p: StyledButtonProps) =>
    css`
      &:hover {
        background-image: url(${p.$hoverImage});
        background-color: var(--absence-button-background-color-hover);
        background-repeat: no-repeat;
        background-size: 1.6rem;
        background-position: left 1rem center;
        color: var(--absence-button-color-hover);
        cursor: pointer;
      }
    `};

  @media (max-width: 760px) {
    padding: 1rem 1rem 1rem 3rem;
  }
`;

const Disable = css<{
  $disabledImage: string;
}>`
  background-image: url(${(p) => p.$disabledImage});
  background-color: var(--absence-button-background-color-disable);
  color: var(--absence-button-color-disable);
`;

interface AbsenceButtonsProps {
  absencesVisible: boolean;
  onTabChange: (isAbsencesVisible: boolean) => void;
}

const AbsenceButtons = ({
  absencesVisible,
  onTabChange,
}: AbsenceButtonsProps) => {
  return (
    <AbsencesButtonsContainer data-testid="absences-buttons-container">
      <StyledButton
        data-testid="absences-button"
        label="Absences"
        $image={planceImage}
        $disabledImage={planceDisableImage}
        $hoverImage={planceDisableImage}
        $isDisable={!absencesVisible}
        onClick={() => onTabChange(true)}
      />
      <StyledButton
        data-testid="sick-leave-button"
        label="Sick Leave"
        $image={heartImage}
        $disabledImage={heartDisableImage}
        $hoverImage={heartDisableImage}
        onClick={() => onTabChange(false)}
        $isDisable={absencesVisible}
      />
    </AbsencesButtonsContainer>
  );
};

export default AbsenceButtons;
