import React from "react";
import styled, { css } from "styled-components";

const Overlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.3);
  z-index: 1002;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const Modal = styled.div`
  background: #ffffff;
  border-radius: 1.875rem;
  padding: 0.1rem;
  min-width: 30rem;
  max-width: 95vw;
  max-height: 90vh;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.12);
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const ModalWrapper = styled.div`
  background: #f8f9fc;
  border-radius: 1.875rem;
  padding: 0rem 1rem;
  min-width: 30rem;
  max-width: 95vw;
  max-height: 90vh;
  box-shadow: 0 4px 24px rgba(0, 0, 0, 0.12);
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const MessageWrapper = styled.div`
  width: 25rem;
  height: 100%;
  padding: 0rem 1rem;
`;

const IconWrapper = styled.div<{ type: string }>`
  svg {
    width: 32px;
    height: 32px;
    ${(p) =>
      p.type === "warning" &&
      css`
        color: #ff9800;
      `}
    ${(p) =>
      p.type === "error" &&
      css`
        color: #f44336;
      `}
  }
`;

const Title = styled.div`
  font-size: 1.1rem;
  font-weight: 500;
  text-align: center;
`;

const Textarea = styled.textarea`
  width: calc(100% - 2rem);
  min-height: 10.75rem;
  border-radius: 1.25rem;
  border: none;
  font: normal normal 300 14px/19px Inter;
  letter-spacing: 0px;
  color: #2d2d2d;
  margin-bottom: 1.2rem;
  resize: none;
  padding: 1rem;
  &:focus {
    outline: none;
    border: none;
  }
`;

const ButtonRow = styled.div`
  display: flex;
  padding: 1rem;
  border-radius: 0px 0px 30px 30px;
  gap: 0.4rem;
  width: 100%;
  justify-content: flex-end;
  background-color: #edeff6;
  margin-top: auto;
`;

const Button = styled.button<{ $primary?: boolean }>`
  min-width: 8.25rem;
  padding: 1.2rem;
  border-radius: 3.75rem;
  border: none;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  background: ${(p) => (p.$primary ? "#186FC7" : "#e3e8f0")};
  color: ${(p) => (p.$primary ? "#fff" : "#1976d2")};
  transition: background 0.2s;
  &:hover {
    background: ${(p) => (p.$primary ? "#3893EF" : "#e3e8f0")};
  }
  &:disabled {
    background: #b0b0b0;
    color: #fff;
    cursor: not-allowed;
  }
`;

const ModalHeaderRow = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  gap: 1rem;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
`;

const icons: Record<string, JSX.Element> = {
  info: (
    <svg
      id="Info_icon"
      data-name="Info icon"
      xmlns="http://www.w3.org/2000/svg"
      width="42"
      height="42"
      viewBox="0 0 42 42"
    >
      <circle
        id="Ellipse_91"
        data-name="Ellipse 91"
        cx="21"
        cy="21"
        r="21"
        fill="#45b6f8"
      />
      <g
        id="Group_1008"
        data-name="Group 1008"
        transform="translate(18.053 7.613)"
      >
        <path
          id="Path_1056"
          data-name="Path 1056"
          d="M30.456,43.579a2.947,2.947,0,0,1-2.947-2.947V29.686a2.947,2.947,0,0,1,2.947-2.947h0A2.947,2.947,0,0,1,33.4,29.686V40.633a2.947,2.947,0,0,1-2.947,2.947Z"
          transform="translate(-27.51 -16.804)"
          fill="#fff"
        />
        <circle
          id="Ellipse_92"
          data-name="Ellipse 92"
          cx="2.947"
          cy="2.947"
          r="2.947"
          transform="translate(0)"
          fill="#fff"
        />
      </g>
    </svg>
  ),
  warning: (
    <svg viewBox="0 0 24 24" fill="none">
      <circle cx="12" cy="12" r="12" fill="currentColor" opacity="0.15" />
      <path
        d="M12 8v4m0 4h.01M12 4a8 8 0 100 16 8 8 0 000-16z"
        stroke="#ff9800"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12 16h.01"
        stroke="#ff9800"
        strokeWidth="2"
        strokeLinecap="round"
      />
    </svg>
  ),
  error: (
    <svg viewBox="0 0 24 24" fill="none">
      <circle cx="12" cy="12" r="12" fill="currentColor" opacity="0.15" />
      <path
        d="M15 9l-6 6m0-6l6 6"
        stroke="#f44336"
        strokeWidth="2"
        strokeLinecap="round"
      />
    </svg>
  ),
};

interface ActionModalProps {
  show: boolean;
  type?: "info" | "warning" | "error";
  title: string;
  textareaValue?: string;
  onTextareaChange?: (v: string) => void;
  onCancel?: () => void;
  onConfirm?: () => void;
  confirmLabel?: string;
  cancelLabel?: string;
  requireMessage?: boolean;
}

export const ActionModal: React.FC<ActionModalProps> = ({
  show,
  type = "info",
  title,
  textareaValue = "",
  onTextareaChange,
  onCancel,
  onConfirm,
  confirmLabel = "Ok",
  cancelLabel,
  requireMessage = false,
}) => {
  if (!show) return null;
  const icon = icons[type] || icons.info;
  const isConfirmDisabled = requireMessage && !textareaValue?.trim();

  return (
    <Overlay>
      <Modal>
        <ModalWrapper>
          <MessageWrapper>
            <ModalHeaderRow>
              <IconWrapper type={type}>{icon}</IconWrapper>
              <Title>{title}</Title>
            </ModalHeaderRow>
            {requireMessage && (
              <div style={{ width: "100%" }}>
                <Textarea
                  value={textareaValue}
                  onChange={(e) => onTextareaChange?.(e.target.value)}
                  autoFocus={requireMessage}
                />
              </div>
            )}
          </MessageWrapper>
          <ButtonRow>
            {cancelLabel && (
              <Button $primary onClick={() => onCancel && onCancel()}>
                {cancelLabel}
              </Button>
            )}
            <Button
              $primary
              onClick={() => onConfirm && onConfirm()}
              disabled={isConfirmDisabled}
            >
              {confirmLabel}
            </Button>
          </ButtonRow>
        </ModalWrapper>
      </Modal>
    </Overlay>
  );
};

export default ActionModal;
