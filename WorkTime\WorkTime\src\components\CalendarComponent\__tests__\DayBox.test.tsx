import { render, screen } from "@testing-library/react";
import DayBox from "../../CalendarComponent/DayBox";
import { AbsenceStatus } from "../../../models/DTOs/absence/AbsenceStatus";
import { AlignmentPosition } from "../types/AlignmentPosition.ts";
import { useUserEmployee } from "../../../features/UserEmployeeContext";

jest.mock("../../../utils/absenceActions", () => ({
  useAbsenceActions: () => ({
    handleApproveAbsence: jest.fn(),
    handleDeclineAbsence: jest.fn(),
    handleDeleteAbsence: jest.fn(),
  }),
}));

jest.mock("../../../features/absences/AbsenceContext", () => ({
  useAbsence: () => ({
    setSelectedAbsence: jest.fn(),
    setIsEditing: jest.fn(),
  }),
}));

jest.mock("../../../features/authentication/AuthContext", () => ({
  useAuth: () => ({ user: { userId: "me" } }),
}));

jest.mock("../../../features/MenuContext", () => ({
  useMenu: () => ({
    toggleMenu: jest.fn(),
    changeView: jest.fn(),
    isOpen: true,
  }),
}));

jest.mock("../../../features/UserEmployeeContext", () => ({
  useUserEmployee: jest.fn(() => ({
    userEmployee: {
      userId: "test-user",
      employeeId: "test-employee",
      name: "Test User",
      payrolls: [],
      permissions: [] as string[],
    },
    setUserEmployee: jest.fn(),
    resetUserEmployee: jest.fn(),
  })),
}));

const mockUseUserEmployee = useUserEmployee as jest.MockedFunction<
  typeof useUserEmployee
>;

describe("DayBox button visibility by status", () => {
  const baseAbsence = {
    id: "a1",
    payrollId: "p1",
    userId: "me",
    employeeName: "Test User Name",
    row: 1,
    positonRounding: AlignmentPosition.Both,
    isHospital: false,
    status: AbsenceStatus.Pending,
    isHighlighted: false,
    startDate: new Date().toISOString(),
    endDate: new Date().toISOString(),
    isOverlapping: false,
    typeIdentifier: "vac",
    comment: "",
    sickNote: "",
    exportStatus: 0,
  };

  test("no approve/decline buttons for approved", () => {
    const data = [{ ...baseAbsence, status: AbsenceStatus.Approved }];
    render(
      <DayBox
        dayDate={new Date()}
        dayData={data as any}
        numberOfRows={2}
        showMyAbsences={false}
      />
    );
    expect(screen.queryByTestId(/approve-absence/)).toBeNull();
    expect(screen.queryByTestId(/decline-absence/)).toBeNull();
  });

  test("show delete button for own non-approved absence when right-aligned", () => {
    const data = [
      {
        ...baseAbsence,
        status: AbsenceStatus.Pending,
        positonRounding: AlignmentPosition.Right,
      },
    ];
    render(
      <DayBox
        dayDate={new Date()}
        dayData={data as any}
        numberOfRows={2}
        showMyAbsences={false}
      />
    );
    expect(
      screen.queryByTestId(`delete-absence-${data[0].payrollId}`)
    ).not.toBeNull();
  });

  test("renders component without errors for admin user with pending absence", () => {
    mockUseUserEmployee.mockReturnValue({
      userEmployee: {
        userId: "test-user",
        employeeId: "test-employee",
        name: "Test User",
        payrolls: [],
        permissions: ["Attendances.Write"] as string[],
      },
      setUserEmployee: jest.fn(),
      resetUserEmployee: jest.fn(),
    });

    const data = [
      {
        ...baseAbsence,
        userId: "other",
        status: AbsenceStatus.Pending,
        positonRounding: AlignmentPosition.Right,
      },
    ];

    const { container } = render(
      <DayBox
        dayDate={new Date()}
        dayData={data as any}
        numberOfRows={2}
        showMyAbsences={false}
      />
    );
    expect(container).toBeInTheDocument();

    mockUseUserEmployee.mockReturnValue({
      userEmployee: {
        userId: "test-user",
        employeeId: "test-employee",
        name: "Test User",
        payrolls: [],
        permissions: [] as string[],
      },
      setUserEmployee: jest.fn(),
      resetUserEmployee: jest.fn(),
    });
  });

  test("shows employee name for EditedByAdmin when showing my absences", () => {
    const data = [{ ...baseAbsence, status: AbsenceStatus.EditedByAdmin }];

    render(
      <DayBox
        dayDate={new Date()}
        dayData={data as any}
        numberOfRows={2}
        showMyAbsences={true}
      />
    );

    expect(screen.getByText("Test User Name")).toBeInTheDocument();
  });

  test("hides delete button for my EditedByAdmin absence (right-aligned)", () => {
    const data = [
      {
        ...baseAbsence,
        status: AbsenceStatus.EditedByAdmin,
        positonRounding: AlignmentPosition.Right,
      },
    ];

    render(
      <DayBox
        dayDate={new Date()}
        dayData={data as any}
        numberOfRows={2}
        showMyAbsences={false}
      />
    );

    expect(
      screen.queryByTestId(`delete-absence-${data[0].payrollId}`)
    ).toBeNull();
  });

  test("hides approve/decline for EditedByAdmin on other user even for admin", () => {
    mockUseUserEmployee.mockReturnValue({
      userEmployee: {
        userId: "test-user",
        employeeId: "test-employee",
        name: "Test User",
        payrolls: [],
        permissions: ["Attendances.Write"] as string[],
      },
      setUserEmployee: jest.fn(),
      resetUserEmployee: jest.fn(),
    });

    const data = [
      {
        ...baseAbsence,
        userId: "other",
        status: AbsenceStatus.EditedByAdmin,
        positonRounding: AlignmentPosition.Right,
      },
    ];

    render(
      <DayBox
        dayDate={new Date()}
        dayData={data as any}
        numberOfRows={2}
        showMyAbsences={false}
      />
    );

    expect(screen.queryByTestId(/approve-absence/)).toBeNull();
    expect(screen.queryByTestId(/decline-absence/)).toBeNull();

    mockUseUserEmployee.mockReturnValue({
      userEmployee: {
        userId: "test-user",
        employeeId: "test-employee",
        name: "Test User",
        payrolls: [],
        permissions: [] as string[],
      },
      setUserEmployee: jest.fn(),
      resetUserEmployee: jest.fn(),
    });
  });
});
