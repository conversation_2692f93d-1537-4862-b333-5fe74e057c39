import { ChangeEvent, useRef, useState } from "react";
import Translator from "../../services/language/Translator";
import styled from "styled-components";

interface TextboxProps
  extends React.DetailedHTMLProps<
    React.AllHTMLAttributes<HTMLDivElement>,
    HTMLDivElement
  > {
  label: string;
  value?: string | number;
  handleChange: (e: ChangeEvent<HTMLInputElement>) => void;
  onKeyDown?: React.KeyboardEventHandler<HTMLInputElement>;
  handleBlur?: (e: React.FocusEvent<HTMLInputElement>) => void;
  validation?: {
    isValid: boolean;
    alertMessage: string;
  };
  "data-testid"?: string;
  readonly?: boolean;
  showImage?: boolean;
  imageSrc?: string;
  imageHoverSrc?: string;
  textToCopy?: string;
  inputType?: string;
}

const Container = styled.div`
  position: relative;
`;

const InputField = styled.input<{ readOnly: boolean; disabled?: boolean }>`
  box-sizing: border-box;
  width: 100%;
  border: 0;
  border-radius: 1.9rem;
  padding: 1.25rem 1.25rem 0.6rem;
  margin: 0.2rem 0 0.2rem 0;
  outline: none;
  color: var(--input-field-color);

  background: ${(props) =>
    props.readOnly
      ? `var(--input-field-background-color-read-only)`
      : `var(--input-field-background-color)`};
  border: ${(props) => (props.readOnly ? "0.16rem solid white" : "none")};

  /* Disabled state styling */
  opacity: ${(props) => (props.disabled ? 0.6 : 1)};
  cursor: ${(props) => (props.disabled ? "not-allowed" : "text")};
  background: ${(props) =>
    props.disabled
      ? `var(--input-field-background-color-disabled, #f5f5f5)`
      : props.readOnly
      ? `var(--input-field-background-color-read-only)`
      : `var(--input-field-background-color)`};
`;

const InputLabel = styled.label<{
  $isFocused: boolean;
  $inputText: string | number | undefined;
  $disabled?: boolean;
}>`
  position: absolute;
  top: ${({ $isFocused, $inputText }) =>
    $isFocused || $inputText ? "30%" : "50%"};
  left: 1rem;
  font-size: ${({ $isFocused, $inputText }) =>
    $isFocused || $inputText ? "0.7rem" : "1rem"};
  transform: translateY(-50%);
  transition: top 0.3s ease-out, font-size 0.3s ease;
  color: ${({ $isFocused, $inputText, $disabled }) =>
    $disabled
      ? "var(--textbox-label-disabled-color, #999)"
      : $isFocused || $inputText
      ? "var(--textbox-label-focused-color)"
      : "var(--textbox-label-blur-color)"};
  user-select: none;
  cursor: ${({ $disabled }) => ($disabled ? "not-allowed" : "pointer")};
  opacity: ${({ $disabled }) => ($disabled ? 0.6 : 1)};
`;

const InputWrapper = styled.div`
  position: relative;
  box-sizing: border-box;
`;

const ImageWrapper = styled.div<{ $hoverImage: string }>`
  position: absolute;
  right: 0.938rem;
  top: 55%;
  transform: translateY(-50%);

  &:hover img {
    content: url(${(p) => p.$hoverImage});
    cursor: pointer;
  }
`;

const AlertMessageDiv = styled.div`
  color: var(--text-box-alert-message-color);
`;

const Textbox = (props: TextboxProps) => {
  const [isFocused, setIsFocused] = useState(false);
  const handleFocus = () => !props.disabled && setIsFocused(true);

  const {
    label,
    value = "",
    inputType,
    handleChange,
    onKeyDown,
    handleBlur,
    validation,
    name,
    "data-testid": testId,
    className,
    readOnly,
    showImage,
    imageSrc,
    imageHoverSrc,
    textToCopy,
    disabled,
  } = props;
  const inputRef = useRef<HTMLInputElement>(null);

  const safeValue = (value as string | number | undefined) ?? "";

  const handleCopy = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
    } catch (err) {
      console.error("Copy failed", err);
    }
  };

  const handleInputBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    setIsFocused(false);
    handleBlur?.(e);
  };

  return (
    <Container className={className} data-testid={`${testId}-container`}>
      <InputWrapper data-testid={`${testId}-input-wrapper`}>
        <InputField
          ref={inputRef}
          type={inputType}
          value={safeValue}
          onChange={handleChange}
          onFocus={handleFocus}
          onBlur={handleInputBlur}
          onKeyDown={onKeyDown}
          name={name}
          data-testid={testId}
          readOnly={readOnly || false}
          disabled={disabled}
        />
        <InputLabel
          $isFocused={isFocused}
          $inputText={safeValue}
          onClick={() => !disabled && inputRef.current?.focus()}
          data-testid={`${testId}-label`}
          $disabled={disabled}
        >
          <Translator getString={label} />
        </InputLabel>

        {showImage && imageSrc && (
          <ImageWrapper
            $hoverImage={imageHoverSrc || ""}
            onClick={() => handleCopy(textToCopy || "")}
            data-testid={`${testId}-image-wrapper`}
          >
            <img
              src={imageSrc}
              alt="Icon"
              width="20"
              height="20"
              data-testid={`${testId}-image`}
            />
          </ImageWrapper>
        )}
      </InputWrapper>
      {validation?.isValid === false && (
        <AlertMessageDiv data-testid={`${testId}-alert`}>
          <Translator getString={validation.alertMessage} />
        </AlertMessageDiv>
      )}
    </Container>
  );
};

export default Textbox;
