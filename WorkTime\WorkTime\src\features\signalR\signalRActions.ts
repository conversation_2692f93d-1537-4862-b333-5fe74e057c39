import { AppDispatch } from "../../app/store";
import { AbsenceHospitalDTO } from "../../models/DTOs/absence/AbsenceHospitalDTO";
import { EmployeePayrollDTO } from "../../models/DTOs/payrolls/EmployeePayrollDTO";
import {
  onPayrollAbsenceUpdated,
  onPayrollAbsenceRemoved,
  onPayrollsAdded,
} from "../payroll/payrollsActions";
import { onPendingEmployeesUpdated } from "../employees/employeesActions";
import { onNotificationsAdded } from "../notifications/notificationsActions";
import { LightPayrollDTO } from "../../models/DTOs/payrolls/LightPayrollDTO";

export const handleSignalREvent = (
  eventName: string,
  data: any,
  dispatch: AppDispatch
) => {
  switch (eventName) {
    case "Notifications.Absences.Declined.Push":
    case "Notifications.Hospital.Added.ByEmployee.Push":
    case "Notifications.Absences.Added.ByEmployee.Push": {
      dispatch(onNotificationsAdded([data]));
      if (data.payload && Array.isArray(data.payload)) {
        try {
          const absences = data.payload as AbsenceHospitalDTO[];
          absences.map((absence) => dispatch(onPayrollAbsenceUpdated(absence)));
        } catch (e) {
          console.error("Failed to parse absence payload:", e);
        }
      }
      break;
    }
    case "Notifications.Absences.Edited.ConfirmedAbsence.ByEmployee.Push":
    case "Notifications.Absences.Edited.ByAdmin.Push":
    case "Notifications.Absences.DeletedRequest.ByEmployee.Push":
    case "Notifications.Absences.Edit.Push":
    case "Notifications.Absences.Declined.Push":
    case "Notifications.Absences.Approved.Push":
    case "Notifications.Absences.DeleteDeclined.Push":
    case "Notifications.Hospital.Approved.Push":
    case "Notifications.Absences.Edited.ConfirmedAbsence.Declined.ByEmployee.Push":
      {
        dispatch(onNotificationsAdded([data]));
        if (data.payload) {
          try {
            const absence = data.payload as AbsenceHospitalDTO;
            dispatch(onPayrollAbsenceUpdated(absence));
          } catch (e) {
            console.error("Failed to parse updated absence payload:", e);
          }
        }
      }
      break;
    case "Notifications.Absences.Delete.Push":
    case "Notifications.Absences.Delete.ByAdmin.Push":
      dispatch(onNotificationsAdded([data]));
      if (data.payload) {
        try {
          const absence = data.payload as AbsenceHospitalDTO;
          dispatch(onPayrollAbsenceRemoved(absence.id || ""));
        } catch (e) {
          console.error("Failed to parse delete absence payload:", e);
        }
      }
      break;
    case "Notifications.Absences.Edited.ByEmployee.PushMany": {
      if (data.payload) {
        try {
          const absences = data.payload as AbsenceHospitalDTO[];
          for (const absence of absences) {
            dispatch(onPayrollAbsenceUpdated(absence));
          }
        } catch (e) {
          console.error("Failed to parse updated absence payload:", e);
        }
      }
      break;
    }
    case "Notifications.PendingEmployees.Updated.Push":
      dispatch(onPendingEmployeesUpdated(data as EmployeePayrollDTO[]));
      break;
    case "Notifications.Absences.Edited.ByAdmin.Push":
      dispatch(onNotificationsAdded([data]));
      if (data.payload) {
        try {
          const absence = data.payload as AbsenceHospitalDTO;
          dispatch(onPayrollAbsenceUpdated(absence));
        } catch (e) {
          console.error("Failed to parse absence payload:", e);
        }
      }
      break;
    case "Notifications.Payrolls.Added.Push":
      dispatch(onPayrollsAdded(data.payload as LightPayrollDTO[]));
      break;
    default:
      dispatch(onNotificationsAdded([data]));
      console.warn("Unhandled SignalR event:", eventName, data);
      break;
  }
};
