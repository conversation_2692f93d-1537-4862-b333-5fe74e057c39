import React, { useContext, useState } from "react";
import Container from "../../components/Container";
import { styled } from "styled-components";
import Label from "../../components/Inputs/Label";
import Image from "../../components/Image";
import UserIconImage from "../../assets/images/notifications/user-icon-small.svg";
import TickIcon from "../../assets/images/notifications/tick-icon.svg";
import CloseNotificationIcon from "../../assets/images/notifications/closeNotificationIcon.svg";
import CloseNotificationIconHover from "../../assets/images/notifications/closeNotificationIconHover.svg";
import MoreNotificationsIcon from "../../assets/images/notifications/moreNotificationsIcon.svg";
import MoreNotificationsIconHover from "../../assets/images/notifications/moreNotificationsIconHover.svg";
import Button from "../../components/Inputs/Button";
import { useAppSelector } from "../../app/hooks";
import { selectNotifications } from "./notificationsActions";
import { NotificationDTO } from "../../models/DTOs/notifications/NotificationDTO";
import { useNotificationDropdown } from "./NotificationContext";
import { useNavigate } from "react-router-dom";
import { getNotificationText } from "../../utils/notificationUtils";
import { translate } from "../../services/language/Translator";

const NotificationsContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
  max-height: 20rem;
  overflow-y: auto;
`;

const NotificationsOuterContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  gap: 0.3rem;
`;

const NotificationRow = styled(Container)<{ isRead: boolean }>`
  position: relative;
  display: flex;
  flex-direction: row;
  gap: 1rem;
  padding: 0.2rem 1.2rem;
  min-height: 3rem;
  border-radius: 1.8rem;
  background-color: ${(props) =>
    props.isRead
      ? "var(--notification-row-background-color-seen)"
      : "var(--notification-row-background-color)"};
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: ${(props) =>
      props.isRead
        ? "var(--notification-row-background-color-seen-hover)"
        : "var(--notification-row-background-color-hover)"};
    cursor: pointer;
  }
`;

const StyledButton = styled(Container)<{ image: string; imageHover: string }>`
  width: 1rem;
  height: 1rem;
  background-image: ${(props) => `url(${props.image})`};
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;

  &:hover {
    background-image: ${(props) => `url(${props.imageHover})`};
  }
`;

const NotificationTitle = styled(Label)`
  display: flex;
  flex-direction: column;
  gap: 1rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: wrap;
  cursor: pointer;
`;

const TitleWrapper = styled.span<{ isRead: boolean }>`
  display: flex;
  flex-direction: row;
  gap: 0.5rem;
  flex: 1;
  min-width: 0;
`;

const UserIcon = styled(Image)`
  translate: 0rem 0.8rem;
  width: 1.3425rem;
  height: 1.3425rem;
`;

const ButtonWrapper = styled(Container)`
  display: flex;
  justify-content: center;
`;

const Notifications = () => {
  const [extendView, setExtendView] = useState(false);
  const { markAsRead } = useNotificationDropdown();
  const navigate = useNavigate();

  const ReadNotification = (notification: NotificationDTO) => {
    markAsRead(notification.id);
    navigate(`${notification.url}`);
  };

  const allNotifications = useAppSelector(selectNotifications).notifications;

  const notifications = extendView
    ? allNotifications
    : allNotifications.filter((n) => !n.isRead);

  return (
    <NotificationsOuterContainer>
      <NotificationsContainer>
        {notifications.length === 0 && (
          <NotificationRow isRead={false}>
            <NotificationTitle>strNoNotifications</NotificationTitle>
          </NotificationRow>
        )}
        {notifications.map((notification) => (
          <NotificationRow
            key={notification.id}
            isRead={notification.isRead}
            onClick={() => ReadNotification(notification)}
          >
            <UserIcon src={notification.isRead ? UserIconImage : TickIcon} />
            <TitleWrapper isRead={notification.isRead}>
              <NotificationTitle>
                {translate(
                  getNotificationText(notification),
                  notification.creatorName ?? ""
                )}
              </NotificationTitle>
            </TitleWrapper>
          </NotificationRow>
        ))}
      </NotificationsContainer>
      <ButtonWrapper>
        <StyledButton
          image={extendView ? CloseNotificationIcon : MoreNotificationsIcon}
          imageHover={
            extendView ? CloseNotificationIconHover : MoreNotificationsIconHover
          }
          onClick={() => setExtendView((v) => !v)}
        />
      </ButtonWrapper>
    </NotificationsOuterContainer>
  );
};

export default Notifications;
