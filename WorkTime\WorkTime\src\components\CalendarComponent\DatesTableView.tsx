import styled from "styled-components";
import { Employee } from "../../features/attendance/useFilteredEmployees";
import Container from "../Container";
import { CalendarHeader } from "./CalendarHeader";
import { DayCell } from "./DayCell";
import { DayOfWeekHeader } from "./DayOfWeekHeader";
import { AbsenceInfo } from "./types/AbsenceInfo";
import { LightPayrollDTO } from "../../models/DTOs/payrolls/LightPayrollDTO";

const CalendarContainer = styled(Container)`
  display: block;
  position: relative;
  align-items: center;
  justify-content: center;
  border-radius: 0em 0em 1.8em 1.8em;
  width: 100%;
  overflow-y: auto;
  height: calc(75vh - 90px);
  padding: 0em 0.6em 0.6em 0.6em;
  background-color: var(--datepicker-view-border);
`;

const DayOfWeekHeaderContainer = styled(Container)`
  flex-shrink: 0;
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: var(--datepicker-view-border);
  width: 100%;
`;

const ContainerDays = styled(Container)`
  position: relative;
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  width: 100%;
  overflow: hidden;
`;

export interface DayInfo {
  dayDate: Date;
  dayNumber: number;
  type: string;
  missingEmployees: AbsenceInfo[];
}

interface DatesTableViewProps {
  setSelectedPayroll: (payroll: LightPayrollDTO | undefined) => void;
  selectedPayroll?: LightPayrollDTO;
  selectedMonth: number;
  selectedYear: number;
  usedLeavesThisMonth: number;
  usedHospitalLeavesThisMonth: number;
  days: DayInfo[];
  maxRowsPerWeek: { [week: number]: number };
  onPrevMonth: () => void;
  onNextMonth: () => void;
  isCurrentDay: (day: number) => boolean;
  selectedEmployee?: Employee;
  hoveredEmployee?: Employee;
  showMyAbsences: boolean;
  onSelectEmployee?: (employee: Employee | undefined) => void;
}

const DatesTableView: React.FC<DatesTableViewProps> = ({
  selectedMonth,
  usedLeavesThisMonth,
  usedHospitalLeavesThisMonth,
  setSelectedPayroll,
  selectedPayroll,
  selectedYear,
  days,
  maxRowsPerWeek,
  onPrevMonth,
  onNextMonth,
  isCurrentDay,
  selectedEmployee,
  hoveredEmployee,
  showMyAbsences,
}) => {
  return (
    <>
      <CalendarHeader
        selectedEmployee={selectedEmployee}
        setSelectedPayroll={setSelectedPayroll}
        selectedPayroll={selectedPayroll}
        data-testid="calendar-header"
        usedLeavesThisMonth={usedLeavesThisMonth}
        usedHospitalLeavesThisMonth={usedHospitalLeavesThisMonth}
        selectedMonth={selectedMonth}
        selectedYear={selectedYear}
        showMyAbsences={showMyAbsences}
        goToPrevMonth={onPrevMonth}
        goToNextMonth={onNextMonth}
      />
      <CalendarContainer data-testid="calendar-container">
        <DayOfWeekHeaderContainer>
          <DayOfWeekHeader data-testid="day-of-week-header" />
        </DayOfWeekHeaderContainer>
        <ContainerDays data-testid="container-days">
          {days.map(({ dayDate, dayNumber, type, missingEmployees }, index) => (
            <DayCell
              dayDate={dayDate}
              data-testid={`day-cell-${index}`}
              key={`day-${index}-${
                selectedPayroll?.workTimeId || "no-payroll"
              }-${selectedMonth}-${selectedYear}`}
              dayNumber={dayNumber}
              isCurrentMonth={type === "currentMonth"}
              isCurrentDay={isCurrentDay(dayNumber)}
              missingEmployees={missingEmployees}
              numberOfRows={maxRowsPerWeek[Math.floor(index / 7)] || 1}
              selectedEmployee={selectedEmployee}
              hoveredEmployee={hoveredEmployee}
              isWeekend={index % 7 >= 5}
              showMyAbsences={showMyAbsences}
            />
          ))}
        </ContainerDays>
      </CalendarContainer>
    </>
  );
};

export default DatesTableView;
