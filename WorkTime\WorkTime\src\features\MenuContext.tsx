import {
  createContext,
  ReactNode,
  useContext,
  useRef,
  useState,
  useCallback,
} from "react";

type MenuEventType = "addressAdded" | "employeeAdded";

interface MenuCallback {
  id: string;
  eventType: MenuEventType;
  callback: (data?: any) => void;
}

interface MenuContextProps {
  isOpen: boolean;
  toggleMenu: () => void;
  closeMenu: () => void;
  changeView: (
    activeView: string,
    openedFrom?: "companyLabel" | "associates" | "other",
    data?: any
  ) => void;
  activeView: string;
  currentPage: string;
  menuOpenedFrom: "companyLabel" | "associates" | "other";
  viewData?: any;
  registerCallback: (
    eventType: MenuEventType,
    callback: (data?: any) => void
  ) => string;
  unregisterCallback: (callbackId: string) => void;
  triggerEvent: (eventType: MenuEventType, data?: any) => void;
}

const MenuContext = createContext<MenuContextProps>({
  isOpen: false,
  toggleMenu: () => {},
  closeMenu: () => {},
  changeView: () => {},
  activeView: "myCompanies",
  currentPage: location.pathname,
  menuOpenedFrom: "other",
  viewData: undefined,
  registerCallback: () => "",
  unregisterCallback: () => {},
  triggerEvent: () => {},
});

export const useMenu = () => {
  return useContext(MenuContext);
};

interface MenuProviderProps {
  children: ReactNode;
}

export const MenuProvider = ({ children }: MenuProviderProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const closeTimeoutRef = useRef<NodeJS.Timeout>();
  const [callbacks, setCallbacks] = useState<MenuCallback[]>([]);
  const callbackIdCounter = useRef(0);

  const [activeView, setActiveView] = useState("myCompanies");
  const [currentPage, setCurrentPage] = useState(location.pathname);
  const [menuOpenedFrom, setMenuOpenedFrom] = useState<
    "companyLabel" | "other" | "associates"
  >("other");
  const [viewData, setViewData] = useState<any>(undefined);

  const registerCallback = useCallback(
    (eventType: MenuEventType, callback: (data?: any) => void): string => {
      const callbackId = `callback_${callbackIdCounter.current++}`;
      const newCallback: MenuCallback = {
        id: callbackId,
        eventType,
        callback,
      };

      setCallbacks((prev) => [...prev, newCallback]);
      return callbackId;
    },
    []
  );

  const unregisterCallback = useCallback((callbackId: string) => {
    setCallbacks((prev) => prev.filter((cb) => cb.id !== callbackId));
  }, []);

  const triggerEvent = useCallback(
    (eventType: MenuEventType, data?: any) => {
      callbacks
        .filter((cb) => cb.eventType === eventType)
        .forEach((cb) => {
          try {
            cb.callback(data);
          } catch (error) {
            console.error(`Error in menu callback ${cb.id}:`, error);
          }
        });
    },
    [callbacks]
  );

  const changeView = (
    activeView: string,
    openedFrom: "companyLabel" | "associates" | "other" = "other",
    data?: any
  ) => {
    setActiveView(activeView);
    setMenuOpenedFrom(openedFrom);
    setViewData(data);
  };

  const toggleMenu = () => {
    if (closeTimeoutRef.current) {
      clearTimeout(closeTimeoutRef.current);
      closeTimeoutRef.current = undefined;
    }
    setIsOpen((prevState: boolean) => !prevState);
    setCurrentPage(location.pathname);
  };

  const closeMenu = () => {
    setIsOpen(false);
    closeTimeoutRef.current = setTimeout(() => {
      changeView("profile", "other");
    }, 400);
  };

  const contextValue = {
    isOpen,
    toggleMenu,
    closeMenu,
    changeView,
    activeView,
    currentPage,
    menuOpenedFrom,
    viewData,
    registerCallback,
    unregisterCallback,
    triggerEvent,
  };

  return (
    <MenuContext.Provider value={contextValue}>{children}</MenuContext.Provider>
  );
};
