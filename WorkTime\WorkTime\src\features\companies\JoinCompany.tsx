import { ChangeEvent, useEffect, useState } from "react";
import { useAppDispatch, useAppSelector } from "../../app/hooks";
import styled, { css, keyframes } from "styled-components";
import { CompanyDTO } from "../../models/DTOs/companies/CompanyDTO";
import {
  onGetSenderaCompanies,
  senderaCompanies,
} from "./senderaCompaniesAction";
import { onJoinCompany } from "./companiesActions";
import Textbox from "../../components/Inputs/Textbox";
import {
  getRequestedCompany,
  joinCompanyRequest,
} from "../../services/companies/companiesService";
import Label from "../../components/Inputs/Label";
import { CompanyRequestRequest } from "../../models/Requests/Companies/CompanyRequestRequest";
import searchImage from "../../assets/images/search.png";
import searchIcon from "../../assets/images/searchIcon.png";
import letterImage from "../../assets/images/letter.png";
import PendingCompany from "./PendingCompany";
import Container from "../../components/Container";

const Wrapper = styled(Container)`
  display: flex;
  flex-direction: column;
  align-items: baseline;
`;

const FilterWrapper = styled(Container)`
  background-color: var(--filter--company-background-color);
  display: flex;
  box-sizing: border-box;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  border: 0.5px solid var(--filter--company-background-color);
  border-radius: 3em;
  margin-bottom: 0.5em;
`;

const SuccesfullRequest = styled(Container)`
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  text-align: center;
  padding: 1rem;
`;

const Image = styled.img<{ $isVisible: boolean }>`
  width: 1.5em;
  display: ${({ $isVisible }) => ($isVisible ? "flex" : "none")};
  height: 1.5em;
  cursor: pointer;
  float: center;
  margin: 0.8rem;
`;

const LetterImage = styled.img`
  width: 2.5em;
  height: 2.5em;
  float: center;
  padding: 0.5em;
`;

const fadeIn = keyframes`
  0% {
    opacity: 0;
    transform: translateX(1.5em);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
`;

const FadeInDiv = styled(Container)<{ $isVisible: boolean }>`
  display: none;
  align-items: center;
  animation: ${fadeIn} 0.3s forwards;

  ${({ $isVisible }) =>
    $isVisible &&
    css`
      display: grid;
      grid-template-columns: 66% 33%;
      grid-column-gap: 1%;
      width: 100%;
      padding: 0 0.2rem 0 0.2rem;
    `}
`;

const SearchWrapper = styled(Container)`
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
`;

const SearchIcon = styled.img<{ $isVisible: boolean }>`
  position: absolute;
  right: 0;
  margin-top: 0.3em;
  padding: 0.3em;
  display: ${({ $isVisible }) => ($isVisible ? "flex" : "none")};
  width: 1.5em;
  height: 1.5em;
  cursor: pointer;
`;

const JoinCompany = () => {
  const dispatch = useAppDispatch();
  const senderaCompaniesState = useAppSelector(senderaCompanies);
  const [pendingCompanies, setPendingCompanies] = useState<CompanyDTO[]>([]);
  const [emailFilter, setEmailFilter] = useState("");
  const [bulstatFilter, setBulstatFilter] = useState("");
  const [isFilterVisible, setIsFilterVisible] = useState(false);
  const [isRequestSent, setIsRequestSent] = useState(false);
  const [companyToRequest, setCompanyToRequest] = useState<CompanyDTO>();

  useEffect(() => {
    dispatch(onGetSenderaCompanies());
  }, [dispatch]);

  useEffect(() => {
    setPendingCompanies(
      senderaCompaniesState.senderaCompanies.filter(
        (company) => company.userStatus === "Pending"
      )
    );
  }, [senderaCompaniesState]);

  const acceptCompany = (company: CompanyDTO) => {
    company && dispatch(onJoinCompany({ ...company }));
    setPendingCompanies(
      pendingCompanies.filter(
        (pendingCompanies) => pendingCompanies.id !== company.id
      )
    );
  };

  const handleEmailChanged = (e: ChangeEvent<HTMLInputElement>) => {
    setEmailFilter(e.currentTarget.value);
  };

  const handleBulstatChanged = (e: ChangeEvent<HTMLInputElement>) => {
    setBulstatFilter(e.currentTarget.value);
  };

  const handleSearchClicked = () => {
    setIsFilterVisible(!isFilterVisible);
  };

  const handleSearchButtonClicked = async () => {
    try {
      await getRequestedCompany(emailFilter, bulstatFilter).then((response) => {
        setCompanyToRequest(response as CompanyDTO);
        setEmailFilter("");
        setBulstatFilter("");
        setIsRequestSent(false);
      });
    } catch (error) {}
  };

  const handleJoinCompany = (companyToRequest: CompanyDTO) => {
    const request = {
      companyId: companyToRequest?.id,
    } as CompanyRequestRequest;

    joinCompanyRequest(request).then(() => {
      setIsRequestSent(true);
      setCompanyToRequest(undefined);
    });
  };

  return (
    <>
      <Wrapper data-testid="wrapper">
        {pendingCompanies.map((company) => (
          <PendingCompany
            data-testid={`pending-company-${company?.id}`}
            company={company}
            label="Accept"
            joinPendingCompany={() => acceptCompany(company)}
            key={company?.id}
          />
        ))}
        <FilterWrapper data-testid="filter-wrapper">
          <Image
            data-testid={`image-${!isFilterVisible}`}
            $isVisible={!isFilterVisible}
            src={searchImage}
            onClick={handleSearchClicked}
          />
          <FadeInDiv $isVisible={isFilterVisible} data-testid="fade-in-div">
            <Textbox
              label="E-mail"
              value={emailFilter}
              handleChange={(e) => handleEmailChanged(e)}
            />
            <SearchWrapper data-testid="search-wrapper">
              <Textbox
                data-testid="textbox"
                label="EIK"
                value={bulstatFilter}
                handleChange={(e) => handleBulstatChanged(e)}
              />
              <SearchIcon
                data-testid="search-icon"
                $isVisible={isFilterVisible}
                src={searchIcon}
                onClick={handleSearchButtonClicked}
              />
            </SearchWrapper>
          </FadeInDiv>
        </FilterWrapper>
        {companyToRequest && !isRequestSent && (
          <PendingCompany
            data-testid={`pending-company-${companyToRequest?.id}`}
            company={companyToRequest}
            label="Ask"
            joinPendingCompany={() => handleJoinCompany(companyToRequest)}
            key={companyToRequest?.id}
          />
        )}
        {isRequestSent && (
          <SuccesfullRequest data-testid="succesfull-request">
            <LetterImage data-testid="letter-image" src={letterImage} />
            <Label data-testid="label">{`SuccessfullJoinRequest ${companyToRequest?.name}`}</Label>
          </SuccesfullRequest>
        )}
      </Wrapper>
    </>
  );
};

export default JoinCompany;
