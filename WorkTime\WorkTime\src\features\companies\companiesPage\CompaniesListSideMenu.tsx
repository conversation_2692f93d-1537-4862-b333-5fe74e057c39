import { ChangeEvent, useEffect, useState } from "react";
import { companiesState, onGetCompanies } from "../companiesActions";
import { useAppDispatch, useAppSelector } from "../../../app/hooks";
import styled from "styled-components";
import Translator from "../../../services/language/Translator";
import { useCompany } from "../CompanyContext";
import { CompanyDTO } from "../../../models/DTOs/companies/CompanyDTO";
import { useNavigate } from "react-router-dom";
import Image from "../../../components/Image";
import TestCompanyLogo from "../../../assets/images/companies/test-company-logo.png";
import Container from "../../../components/Container";
import { useMenu } from "../../MenuContext";
import Searchbar from "../../../components/Inputs/Searchbar";
import sortImage from "../../../assets/images/arrows/sort.png";
import sortImageHover from "../../../assets/images/arrows/sort-hover.png";
import { companyFilterService } from "../../../services/companies/CompanyFilterService";
import { setCompanyLocalStorage } from "../../../services/companies/companiesService";
import { DefaultPermissions } from "../../../constants/permissions";
import { UserEmployeeDTO } from "../../../models/DTOs/users/UserEmployeeDTO";
import { initUserEmployee } from "../../../services/users/userService";
import { useUserEmployee } from "../../UserEmployeeContext";
import { loadEmployeePayrollsAction } from "../../payroll/employeePayrollActions";
import { getEmployeePayrolls } from "../../../services/employees/employeePayrollsService";

const CompaniesContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const SearchbarContainer = styled.div`
  display: inline-block;
  position: relative;
  box-sizing: border-box;
  justify-content: center;
  width: 100%;
  margin-bottom: 1rem;
`;

const SortImage = styled.div<{ ascending: boolean }>`
  position: absolute;
  background-size: cover;
  height: 2rem;
  width: 2rem;
  right: 0.7rem;
  top: 50%;
  transform: translateY(-50%)
    ${({ ascending }) =>
      ascending ? "rotate(180deg) scaleX(-1)" : "rotate(0deg)"};
  background-image: url(${sortImage});
  transition: transform 0.3s ease-in-out;
  cursor: pointer;

  &:hover {
    background-image: url(${sortImageHover});
  }
`;

const CompanyRow = styled(Container)`
  width: 80%;
  cursor: pointer;
  align-items: baseline;
  display: flex;
  padding: 0.5rem 0 0.5rem 0;
  align-items: center;
  flex-direction: row;
`;

const CompanyImage = styled(Image)`
  height: 1.9rem;
  width: 1.9rem;
  vertical-align: middle;
  margin-right: 0.8rem;
`;

const InfoContainer = styled(Container)`
  display: flex;
  flex-direction: column;
  align-items: baseline;
`;

const CompanyName = styled(Container)`
  color: var(--company-name-label-color);
  float: left;
  border: none;
  font-size: 0.75rem;
`;

const CompanyBulstat = styled(Container)`
  color: var(--company-bulstat-label-color);
  float: left;
  border: none;
  font-size: 0.65rem;
`;

const CompaniesListSideMenu = () => {
  const dispatch = useAppDispatch();
  const { activeCompanies } = useAppSelector(companiesState);
  const { setCompany } = useCompany();
  const { closeMenu } = useMenu();
  const navigate = useNavigate();
  const [textInput, setTextInput] = useState("");
  const { companyFilter, mapToFilteredDTOs } = companyFilterService();
  const [filteredCompanies, setFilteredCompanies] =
    useState<CompanyDTO[]>(activeCompanies);
  const [sortAscending, setSortAscending] = useState(true);
  const { setUserEmployee } = useUserEmployee();

  useEffect(() => {
    dispatch(onGetCompanies());
  }, [dispatch]);

  useEffect(() => {
    const filteredCompaniesArray = companyFilter(textInput);
    setFilteredCompanies(
      [...filteredCompaniesArray].sort((a, b) => {
        const nameA = a.name.toLowerCase();
        const nameB = b.name.toLowerCase();

        if (nameA < nameB) return sortAscending ? -1 : 1;
        if (nameA > nameB) return sortAscending ? 1 : -1;

        return 0;
      })
    );
  }, [activeCompanies]);

  const selectCompany = async (company: CompanyDTO) => {
    setCompany(company);

    setCompanyLocalStorage(company);

    const employeePayrollsResponse = await getEmployeePayrolls(company.id);
    dispatch(loadEmployeePayrollsAction(employeePayrollsResponse));

    closeMenu();
    await initUserEmployee().then((response: UserEmployeeDTO) => {
      setUserEmployee(response);
      if (response.permissions.includes(DefaultPermissions.Employees.Write))
        navigate(`/${company.id}/employees`);
      else
        navigate(
          `/${company.id}/employees/0/${response.employeeId}/${
            employeePayrollsResponse[0]?.payrollId ?? ""
          }`
        );
    });
  };

  const handleSort = () => {
    const sortedCompanies = [...filteredCompanies].sort((a, b) => {
      const nameA = a.name.toLowerCase();
      const nameB = b.name.toLowerCase();

      if (nameA < nameB) return sortAscending ? 1 : -1;
      if (nameA > nameB) return sortAscending ? -1 : 1;

      return 0;
    });

    setFilteredCompanies(sortedCompanies);
    setSortAscending(!sortAscending);
  };

  const companiesFilter = (searchText: string) => {
    const filteredCompaniesArray = companyFilter(searchText);
    const mappedFilteredData = mapToFilteredDTOs(filteredCompaniesArray);
    setFilteredCompanies(mappedFilteredData);
  };

  const handleSearchTextChanged = (e: ChangeEvent<HTMLInputElement>) => {
    const searchText = e.target.value;
    setTextInput(searchText);
    companiesFilter(searchText);
  };

  return (
    <CompaniesContainer data-testid="companies-container">
      <SearchbarContainer data-testid="searchbar-container">
        <Searchbar
          handleChange={handleSearchTextChanged}
          value={textInput}
          type="text"
          label="strGetInput"
          placeholder={""}
          height={3}
          direction="right"
        />
        <SortImage
          onClick={handleSort}
          ascending={sortAscending}
          data-testid="sort-image"
        />
      </SearchbarContainer>
      {filteredCompanies.map((company) => (
        <CompanyRow
          key={company.id}
          onClick={() => selectCompany(company)}
          data-testid={`company-row-${company.id}`}
        >
          <CompanyImage src={TestCompanyLogo} data-testid="company-image" />
          <InfoContainer>
            <CompanyName data-testid="company-name">{company.name}</CompanyName>
            <CompanyBulstat data-testid="company-bulstat">
              <Translator getString="EIK:" /> {company.bulstat}
            </CompanyBulstat>
          </InfoContainer>
        </CompanyRow>
      ))}
    </CompaniesContainer>
  );
};

export default CompaniesListSideMenu;
