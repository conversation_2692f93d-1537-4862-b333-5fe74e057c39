import { useState, useEffect } from "react";
import styled from "styled-components";
import { useAppDispatch } from "../../../app/hooks";
import Container from "../../../components/Container";
import Button from "../../../components/Inputs/Button";
import MainWindowContainer from "../../../components/MainWindowContainer";
import { LOCAL_STORAGE_COMPANY_ID } from "../../../constants/local-storage-constants";
import { NewAddressDTO } from "../../../models/DTOs/newEmployee/NewAddressDTO";
import { editEmployee } from "../../../services/employees/employeesService";
import { translate } from "../../../services/language/Translator";
import { useMenu } from "../../MenuContext";
import { EditEmployeeDTO } from "../../../models/DTOs/editEmployee/EditEmployeeDTO";
import {
  AddressDTO,
  AddressPurpose,
} from "../../../models/DTOs/address/AddressDTO";
import { onEmployeeEdited } from "../employeesActions";
import NewAddresses from "../newEmployee/NewAddress";

const MainContainer = styled(MainWindowContainer)`
  margin: 0 auto;
  width: 100%;
  margin-bottom: 7rem;
`;

const StepsContainer = styled(Container)`
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, auto);
`;

const StepButton = styled(Button)<{ isSelected: boolean }>`
  margin: 1px;
  padding: 0.8rem 0.5rem 0.8rem 1.8rem;
  text-align: left;
  font-size: 0.9rem;
  background-color: ${(p) =>
    p.isSelected
      ? "var(--profile-button-background-color)"
      : "var(--profile-button-background-color-disable)"};
  color: ${(p) =>
    p.isSelected
      ? "var(--profile-button-color)"
      : "var(--profile-button-color-disable)"};
  opacity: ${(p) => (p.isSelected ? "1" : "0.7")};

  &:hover {
    background-color: var(--profile-button-background-color-hover);
    color: var(--profile-button-color-hover);
    cursor: pointer;
  }
`;

const ContentContainer = styled(Container)`
  margin-top: 1rem;
`;

const NavigationContainer = styled(Container)`
  display: flex;
  margin-bottom: 1rem;
  justify-content: center;
  position: fixed;
  bottom: 0;
  right: 0;
  width: 100%;
  z-index: 5;
`;

const NextButton = styled(Button)`
  width: 90%;
`;
export interface EditEmployeeFormData {
  personalData: FormValidationState | null;
  identityCardData: FormValidationState | null;
  addressCorrespondence: FormValidationState | null;
  addressRemoteWork: FormValidationState | null;
  addressAbroad: FormValidationState | null;
}
interface FormValidationState {
  isValid: boolean;
  data: any;
}

const EditEmployeeAddressesSideMenu = () => {
  const dispatch = useAppDispatch();
  const { toggleMenu, changeView, viewData } = useMenu();
  const [activeStep, setActiveStep] = useState(viewData?.step ?? 1);
  const [isLoading, setIsLoading] = useState(false);
  const [isAddressCorrespondenceChanged, setIsAddressCorrespondenceChanged] =
    useState(false);
  const [isAddressRemoteWorkChanged, setIsAddressRemoteWorkChanged] =
    useState(false);
  const [isAddressAbroadChanged, setIsAddressAbroadChanged] = useState(false);

  const convertEmployeeAddressToAddressData = (
    addresses: AddressDTO[] | undefined,
    purpose: AddressPurpose
  ): NewAddressDTO => {
    var address = addresses?.find(
      (address) => address.purpose.identifier === purpose
    );
    if (addresses === undefined || !address) {
      return {
        id: undefined,
        city: null,
        postalCode: "",
        employeeId: "",
        municipality: null,
        district: null,
        street: "",
        block: "",
        apartment: "",
        phone: "",
        workPhone: "",
        email: "",
        workEmail: "",
        country: undefined,
        purpose: purpose,
        description: "",
        neighborhood: "",
        cityName: "",
        districtName: "",
        municipalityName: "",
      };
    }
    return {
      id: address?.id || undefined,
      postalCode: address?.postalCode || "",
      employeeId: address?.employeeId || "",
      municipality: {
        id: address?.municipality?.id || "",
        name: address?.municipality?.name || "",
        districtId: address?.district?.id || "",
      },
      district: {
        id: address?.district?.id || "",
        name: address?.district?.name || "",
      },
      city: {
        id: address?.city?.id || "",
        name: address?.city?.name || "",
        postCode: "",
        EKATTECode: 0,
        municipalityId: "",
      },
      street: address?.street || "",
      block: address?.block || "",
      apartment: address?.apartment || "",
      phone: address?.phone || "",
      workPhone: address?.workPhone || "",
      email: address?.email || "",
      workEmail: address?.workEmail || "",
      country: Number(address?.country) || 0,
      purpose: purpose,
      description: address?.description || "",
      neighborhood: address?.neighborhood || "",
      cityName: address?.cityName || "",
      districtName: address?.districtName || "",
      municipalityName: address?.municipalityName || "",
    };
  };

  const addressesList = viewData?.addressesList as AddressDTO[];

  const [formData, setFormData] = useState<EditEmployeeFormData>({
    personalData: null,
    identityCardData: null,
    addressCorrespondence: {
      isValid: false,
      data: convertEmployeeAddressToAddressData(
        addressesList,
        AddressPurpose.ForContact
      ),
    },
    addressRemoteWork: {
      isValid: false,
      data: convertEmployeeAddressToAddressData(
        addressesList,
        AddressPurpose.ForRemoteWork
      ),
    },
    addressAbroad: {
      isValid: false,
      data: convertEmployeeAddressToAddressData(
        addressesList,
        AddressPurpose.Abroad
      ),
    },
  });

  useEffect(() => {
    if (addressesList) {
      setFormData((prev) => ({
        ...prev,
        personalData: {
          isValid: false,
          data: null,
        },
        identityCardData: {
          isValid: false,
          data: null,
        },
        addressCorrespondence: {
          isValid: false,
          data: convertEmployeeAddressToAddressData(
            addressesList,
            AddressPurpose.ForContact
          ),
        },
        addressRemoteWork: {
          isValid: false,
          data: convertEmployeeAddressToAddressData(
            addressesList,
            AddressPurpose.ForRemoteWork
          ),
        },
        addressAbroad: {
          isValid: false,
          data: convertEmployeeAddressToAddressData(
            addressesList,
            AddressPurpose.Abroad
          ),
        },
      }));
    }
  }, [addressesList]);

  const isCurrentStepValid = () => {
    switch (activeStep) {
      case AddressPurpose.ForContact:
        return formData.addressCorrespondence?.isValid ?? false;
      case AddressPurpose.ForRemoteWork:
        return formData.addressRemoteWork?.isValid ?? false;
      case AddressPurpose.Abroad:
        return formData.addressAbroad?.isValid ?? false;
      default:
        return false;
    }
  };

  const handleAddressCorrespondenceChange = () => {
    setIsAddressCorrespondenceChanged(true);
  };
  const handleAddressRemoteWorkChange = () => {
    setIsAddressRemoteWorkChanged(true);
  };
  const handleAddressAbroadChange = () => {
    setIsAddressAbroadChanged(true);
  };

  const handleAddressValidation = (isValid: boolean, data: AddressDTO) => {
    setFormData((prev) => ({
      ...prev,
      [activeStep === AddressPurpose.ForContact
        ? "addressCorrespondence"
        : activeStep === AddressPurpose.ForRemoteWork
        ? "addressRemoteWork"
        : "addressAbroad"]: { isValid, data },
    }));
  };

  const steps = [
    {
      id: AddressPurpose.ForContact,
      label: translate("For contact"),
      component: (
        <NewAddresses
          onValidation={handleAddressValidation}
          onChange={handleAddressCorrespondenceChange}
          data={formData.addressCorrespondence?.data as NewAddressDTO}
          isNewEmployeeFlow={true}
        />
      ),
    },
    {
      id: AddressPurpose.ForRemoteWork,
      label: translate("For remote work"),
      component: (
        <NewAddresses
          onValidation={handleAddressValidation}
          onChange={handleAddressRemoteWorkChange}
          data={formData.addressRemoteWork?.data as NewAddressDTO}
          isNewEmployeeFlow={true}
        />
      ),
    },
    {
      id: AddressPurpose.Abroad,
      label: translate("Abroad"),
      component: (
        <NewAddresses
          onValidation={handleAddressValidation}
          onChange={handleAddressAbroadChange}
          data={formData.addressAbroad?.data as NewAddressDTO}
          isNewEmployeeFlow={true}
        />
      ),
    },
  ];

  const handleStepClick = (stepId: number) => {
    setActiveStep(stepId);
  };

  const handleSubmit = async () => {
    if (!isCurrentStepValid()) {
      return;
    }

    setIsLoading(true);
    const companyId = localStorage.getItem(LOCAL_STORAGE_COMPANY_ID) ?? "";

    const editEmployeeData: EditEmployeeDTO = {
      employeeId: viewData?.employeeId,
      payrollId: viewData?.payrollId,
      companyId: companyId,
      personalData: null,
      identityCardData: null,
      addressForCorrespondence: isAddressCorrespondenceChanged
        ? (formData.addressCorrespondence?.data as NewAddressDTO)
        : null,
      addressForRemoteWork: isAddressRemoteWorkChanged
        ? (formData.addressRemoteWork?.data as NewAddressDTO)
        : null,
      addressForAbroad: isAddressAbroadChanged
        ? (formData.addressAbroad?.data as NewAddressDTO)
        : null,
    };

    const editedEmployee = await editEmployee(editEmployeeData);
    //to do да се dispatch-ва когато се удобри промяната
    dispatch(onEmployeeEdited(editedEmployee));

    setIsLoading(false);
    toggleMenu();
    changeView("employees", "other");
  };

  return (
    <MainContainer data-testid="edit-employee-side-menu">
      <StepsContainer data-testid="steps-container">
        {steps.map((step) => (
          <StepButton
            key={step.id}
            label={step.label}
            isSelected={activeStep === step.id}
            disabled={false}
            onClick={() => handleStepClick(step.id)}
            data-testid={`step-button-${step.id}`}
          />
        ))}
      </StepsContainer>

      <ContentContainer data-testid="content-container">
        <div key={activeStep}>
          {steps.find((step) => step.id === activeStep)?.component}
        </div>
      </ContentContainer>

      <NavigationContainer data-testid="navigation-container">
        {activeStep === AddressPurpose.Abroad ? (
          <NextButton
            label="Edit"
            onClick={handleSubmit}
            disabled={!isCurrentStepValid() || !isAddressAbroadChanged}
            data-testid="address-abroad-data-button"
          />
        ) : activeStep === AddressPurpose.ForRemoteWork ? (
          <NextButton
            label="Edit"
            onClick={handleSubmit}
            disabled={!isCurrentStepValid() || !isAddressRemoteWorkChanged}
            data-testid="address-remote-work-button"
          />
        ) : (
          <NextButton
            label="Edit"
            onClick={handleSubmit}
            disabled={!isCurrentStepValid() || !isAddressCorrespondenceChanged}
            data-testid="address-correspondence-button"
          />
        )}
      </NavigationContainer>
    </MainContainer>
  );
};

export default EditEmployeeAddressesSideMenu;
