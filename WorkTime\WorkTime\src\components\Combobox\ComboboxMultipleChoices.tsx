import React, {
  useEffect,
  useMemo,
  useRef,
  useState,
  useCallback,
  useLayoutEffect,
} from "react";
import checkActive from "../../assets/images/combobox/check-active.svg";
import checkHover from "../../assets/images/combobox/check-hover.svg";
import checkInactive from "../../assets/images/combobox/check-inactive.svg";
import TriangularArrow from "../../assets/images/combobox/arrow-open.svg";
import ArrowHover from "../../assets/images/combobox/arrow-hover.svg";
import Translator, { translate } from "../../services/language/Translator";
import {
  ComboboxContainer,
  SelectedDisplay,
  TriangularArrowContainer,
  DropdownList,
  OptionItem,
  CheckboxContainer,
  OptionText,
  CheckmarkImage,
  RoundCheckbox,
  SelectedCount,
} from "./ComboboxStyles";
import Container from "../Container";

interface Option {
  id: string | number;
  label: string;
  value: any;
}

interface ComboboxMultipleChoicesProps {
  options: Option[];
  selectedValues: any[];
  onChange: (selected: any[]) => void;
  placeholder?: string;
  className?: string;
  width?: string;
  height?: string;
  conditionalMargin?: boolean;
  idCondition?: number[];
}

const ComboboxMultipleChoices: React.FC<ComboboxMultipleChoicesProps> = ({
  options,
  selectedValues,
  onChange,
  placeholder,
  width,
  height,
  conditionalMargin = false,
  idCondition,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [hoveredOption, setHoveredOption] = useState<string | null>(null);
  const [isHoveredHeader, setIsHoveredHeader] = useState(false);
  const [fontSize, setFontSize] = useState(16);
  const [shouldTruncate, setShouldTruncate] = useState(false);

  const containerRef = useRef<HTMLDivElement | null>(null);
  const textRef = useRef<HTMLDivElement | null>(null);
  const measureRef = useRef<HTMLSpanElement | null>(null);

  const toggleDropdown = useCallback(() => {
    setIsOpen((prev) => !prev);
  }, []);

  const handleOptionClick = useCallback(
    (value: any) => {
      const newSelected = selectedValues.includes(value)
        ? selectedValues.filter((v) => v !== value)
        : [...selectedValues, value];
      onChange(newSelected);
    },
    [selectedValues, onChange]
  );

  const displayText = useMemo(() => {
    if (selectedValues.length === 0) return translate(placeholder || "");
    if (selectedValues.length === 1) {
      const option = options.find(
        (option) => option.value === selectedValues[0]
      );
      return option?.label || "";
    }
    return `${selectedValues.length} items selected`;
  }, [selectedValues, options, placeholder]);

  const measureTextWidth = useCallback((text: string, fontSize: number) => {
    if (!measureRef.current) return 0;

    measureRef.current.style.fontSize = `${fontSize}px`;
    measureRef.current.textContent = text;
    return measureRef.current.offsetWidth;
  }, []);

  const getAvailableWidth = useCallback(() => {
    if (!containerRef.current) return 0;

    const containerWidth = containerRef.current.offsetWidth;
    const padding = 32;
    const arrowWidth = 10;

    return containerWidth - padding - arrowWidth;
  }, []);

  const adjustTextDisplay = useCallback(() => {
    if (!displayText || typeof displayText !== "string") return;

    const availableWidth = getAvailableWidth();
    if (availableWidth <= 0) return;

    let currentFontSize = 16;
    let currentWidth = measureTextWidth(displayText, currentFontSize);

    while (currentWidth > availableWidth && currentFontSize > 10) {
      currentFontSize -= 0.5;
      currentWidth = measureTextWidth(displayText, currentFontSize);
    }

    const needsTruncation =
      currentWidth > availableWidth && currentFontSize <= 10;

    setFontSize(currentFontSize);
    setShouldTruncate(needsTruncation);
  }, [displayText, measureTextWidth, getAvailableWidth]);

  useLayoutEffect(() => {
    if (typeof displayText === "string") {
      adjustTextDisplay();
    } else {
      setFontSize(16);
      setShouldTruncate(false);
    }
  }, [displayText, adjustTextDisplay, width]);

  useEffect(() => {
    const handleResize = () => {
      if (typeof displayText === "string") {
        adjustTextDisplay();
      }
    };

    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [displayText, adjustTextDisplay]);

  const getCheckmarkImage = (optionValue: any) => {
    const isSelected = selectedValues.includes(optionValue);
    const isHovered = hoveredOption === optionValue;

    if (isSelected) {
      return checkActive;
    } else if (isHovered) {
      return checkHover;
    } else {
      return checkInactive;
    }
  };

  const getTriangleImage = () => {
    return isHoveredHeader ? ArrowHover : TriangularArrow;
  };

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        containerRef.current &&
        !containerRef.current.contains(event.target as Node) &&
        !isOpen
      ) {
        setIsOpen(false);
      }
    };
    window.addEventListener("mousedown", handleClickOutside);
    return () => {
      window.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  return (
    <ComboboxContainer ref={containerRef} data-testid="combobox-container">
      <span
        ref={measureRef}
        style={{
          position: "absolute",
          visibility: "hidden",
          whiteSpace: "nowrap",
          fontFamily: "Segoe UI",
          fontWeight: 400,
        }}
      />

      <SelectedDisplay
        onClick={toggleDropdown}
        isOpen={isOpen && options.length > 0}
        onMouseEnter={() => setIsHoveredHeader(true)}
        onMouseLeave={() => setIsHoveredHeader(false)}
        width={width}
        height={height}
        data-testid="selected-display"
      >
        <Container
          ref={textRef}
          style={{
            fontSize: `${fontSize}px`,
            overflow: shouldTruncate ? "hidden" : "visible",
            textOverflow: shouldTruncate ? "ellipsis" : "clip",
            whiteSpace: "nowrap",
            flex: 1,
            minWidth: 0,
          }}
        >
          {typeof displayText === "string" ? (
            displayText
          ) : selectedValues.length > 1 ? (
            <SelectedCount>
              {`${selectedValues.length} `}
              <Translator getString="strItemsSelected" />
            </SelectedCount>
          ) : (
            <Translator
              getString={displayText}
              data-testid="display-text-translator"
            />
          )}
        </Container>
        <TriangularArrowContainer
          src={getTriangleImage()}
          isHovered={isHoveredHeader}
          data-testid="dropdown-arrow"
        />
      </SelectedDisplay>

      <DropdownList
        isOpen={isOpen && options.length > 0}
        width={width}
        data-testid="dropdown-list"
      >
        {options.map((option) => (
          <OptionItem
            key={option.id}
            onClick={() => handleOptionClick(option.value)}
            onMouseEnter={() => setHoveredOption(option.value)}
            onMouseLeave={() => setHoveredOption(null)}
            data-testid={`option-item-${option.id}`}
            conditionalMargin={
              conditionalMargin && idCondition?.includes(Number(option.id))
            }
          >
            <CheckboxContainer data-testid={`checkbox-container-${option.id}`}>
              <RoundCheckbox
                checked={selectedValues.includes(option.value)}
                data-testid={`checkbox-${option.id}`}
              >
                <CheckmarkImage
                  src={getCheckmarkImage(option.value)}
                  isHovered={
                    !selectedValues.includes(option.value) &&
                    hoveredOption === option.value
                  }
                  data-testid={`checkmark-${option.id}`}
                />
              </RoundCheckbox>
            </CheckboxContainer>
            <OptionText>{option.label}</OptionText>
          </OptionItem>
        ))}
      </DropdownList>
    </ComboboxContainer>
  );
};

export default ComboboxMultipleChoices;
