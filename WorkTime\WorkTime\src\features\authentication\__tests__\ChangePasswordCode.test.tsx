import { render, fireEvent, waitFor, screen } from "@testing-library/react";
import "@testing-library/jest-dom";
import ChangePasswordCode from "../ChangePasswordCode";
import { ModalProvider } from "../../../components/PopUp/ActionModalContext";
jest.mock("../../../services/connectionService", () => ({
  authenticatedPost: jest.fn(),
}));
import * as connectionService from "../../../services/connectionService";
import { translate } from "../../../services/language/Translator";

const mockNavigate = jest.fn();
jest.mock("react-router-dom", () => {
  const actual = jest.requireActual("react-router-dom");
  return {
    ...actual,
    useNavigate: () => mockNavigate,
  };
});

const mockSetUser = jest.fn();
jest.mock("../AuthContext", () => ({
  useAuth: () => ({
    user: {
      userId: "1",
      firstName: "Test",
      secondName: "User",
      lastName: "Tester",
      email: "<EMAIL>",
      hasSignedIn: false,
      workTimeRoleName: undefined,
    },
    setUser: mockSetUser,
    resetUser: jest.fn(),
    isLoading: false,
    setIsLoading: jest.fn(),
  }),
}));

describe("ChangePasswordCode", () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorage.clear();
  });

  const setup = () => {
    return render(
      <ModalProvider>
        <ChangePasswordCode />
      </ModalProvider>
    );
  };

  it("enables the button only when terms accepted and strong matching passwords are provided", async () => {
    setup();

    const passwordInputs = screen.getAllByTestId("password-box");
    const oldPasswordInput = passwordInputs[0] as HTMLInputElement;
    const newPasswordInput = passwordInputs[1] as HTMLInputElement;
    const confirmPasswordInput = screen.getByTestId(
      "confirm-password-box"
    ) as HTMLInputElement;

    const changeButton = screen.getByTestId(
      "change-password-button"
    ) as HTMLButtonElement;

    expect(changeButton).toBeDisabled();

    fireEvent.change(oldPasswordInput, { target: { value: "OldPass1!" } });
    fireEvent.change(newPasswordInput, { target: { value: "Aa1!aaaa" } });
    fireEvent.change(confirmPasswordInput, { target: { value: "Aa1!aaaa" } });

    expect(changeButton).toBeDisabled();

    const termsCheckbox = screen.getByTestId("checkbox-input-acceptTerms");
    fireEvent.click(termsCheckbox);

    expect(changeButton).toBeEnabled();
  });

  it("submits, shows success modal, and navigates on confirm", async () => {
    const postSpy = connectionService.authenticatedPost as unknown as jest.Mock;
    postSpy.mockResolvedValue({});

    setup();

    const [oldPasswordInput, newPasswordInput] = screen.getAllByTestId(
      "password-box"
    ) as HTMLInputElement[];
    const confirmPasswordInput = screen.getByTestId(
      "confirm-password-box"
    ) as HTMLInputElement;

    fireEvent.change(oldPasswordInput, { target: { value: "OldPass1!" } });
    fireEvent.change(newPasswordInput, { target: { value: "Aa1!aaaa" } });
    fireEvent.change(confirmPasswordInput, { target: { value: "Aa1!aaaa" } });

    fireEvent.click(screen.getByTestId("checkbox-input-acceptTerms"));

    const changeButton = screen.getByTestId(
      "change-password-button"
    ) as HTMLButtonElement;
    expect(changeButton).toBeEnabled();
    fireEvent.click(changeButton);

    await waitFor(() => expect(postSpy).toHaveBeenCalled());
    expect(postSpy).toHaveBeenCalledWith("sso/change-password", {
      oldPassword: "OldPass1!",
      password: "Aa1!aaaa",
    });

    const expectedTitle = translate("Password changed successfully");
    const modalTitle = await screen.findByText(expectedTitle);
    expect(modalTitle).toBeInTheDocument();

    fireEvent.click(screen.getByText("Ok"));
    await waitFor(() => expect(mockNavigate).toHaveBeenCalledWith("/"));

    expect(mockSetUser).toHaveBeenCalledWith(
      expect.objectContaining({ hasSignedIn: true })
    );
  });
});
