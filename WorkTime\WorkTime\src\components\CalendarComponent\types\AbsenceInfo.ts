import { AbsenceExportStatus } from "../../../models/DTOs/absence/AbsenceExportStatus.ts";
import { AbsenceStatus } from "../../../models/DTOs/absence/AbsenceStatus.ts";
import { EventType } from "../../../models/DTOs/absence/EventType.ts";
import { AlignmentPosition } from "./AlignmentPosition.ts";

export interface AbsenceInfo {
  id: string;
  payrollId: string;
  userId: string;
  employeeName: string;
  row: number;
  positonRounding: AlignmentPosition;
  isHospital: boolean;
  status: AbsenceStatus;
  isHighlighted?: boolean;
  startDate: string;
  endDate: string;
  isOverlapping: boolean;
  typeIdentifier: EventType;
  comment: string;
  sickNote?: string;
  exportStatus: AbsenceExportStatus;
}
