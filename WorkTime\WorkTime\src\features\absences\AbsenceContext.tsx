import {
  createContext,
  useContext,
  ReactNode,
  useState,
  useEffect,
  useRef,
} from "react";
import { useCompany } from "../companies/CompanyContext";
import { useAuth } from "../authentication/AuthContext";
import { AbsenceInfo } from "../../components/CalendarComponent/types/AbsenceInfo";
import { useMenu } from "../MenuContext";
import { AbsenceStatus } from "../../models/DTOs/absence/AbsenceStatus";
import { useSearchParams } from "react-router-dom";

interface AbsenceContextType {
  selectedAbsence: AbsenceInfo | null;
  setSelectedAbsence: (absence: AbsenceInfo | null) => void;
  isEditing: boolean;
  setIsEditing: (isEditing: boolean) => void;
  resetAbsence: () => void;
}

const AbsenceContext = createContext<AbsenceContextType>({
  selectedAbsence: null,
  setSelectedAbsence: () => {},
  isEditing: false,
  setIsEditing: () => {},
  resetAbsence: () => {},
});

export const useAbsence = () => useContext(AbsenceContext);

interface AbsenceProviderProps {
  children: ReactNode;
}

export const AbsenceProvider = ({ children }: AbsenceProviderProps) => {
  const [selectedAbsence, setSelectedAbsence] = useState<AbsenceInfo | null>(
    null
  );
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const { company } = useCompany();
  const { user } = useAuth();
  const { isOpen } = useMenu();
  const [, setSearchParams] = useSearchParams();
  const hasInitializedSyncRef = useRef(false);
  const initialAbsenceIdRef = useRef<string | null>(
    new URLSearchParams(window.location.search).get("absenceId")
  );
  const hasSetSelectionRef = useRef(false);

  const resetAbsence = () => {
    setSelectedAbsence(null);
    setIsEditing(false);
  };

  useEffect(() => {
    resetAbsence();
  }, [company.id]);

  useEffect(() => {
    resetAbsence();
  }, [user]);

  useEffect(() => {
    if (!selectedAbsence) {
      setIsEditing(false);
      return;
    }
    const isMine = selectedAbsence.userId === user.userId;
    const isPending = selectedAbsence.status === AbsenceStatus.Pending;
    setIsEditing(isMine && isPending);
  }, [selectedAbsence, user.userId]);

  useEffect(() => {
    if (!isOpen) {
      resetAbsence();
    }
  }, [isOpen]);

  useEffect(() => {
    if (!hasInitializedSyncRef.current) {
      hasInitializedSyncRef.current = true;
      return;
    }

    const nextParams = new URLSearchParams(window.location.search);
    if (selectedAbsence?.id) {
      hasSetSelectionRef.current = true;
      nextParams.set("absenceId", selectedAbsence.id);
      setSearchParams(nextParams, { replace: true });
      return;
    }

    if (initialAbsenceIdRef.current && !hasSetSelectionRef.current) {
      return;
    }

    nextParams.delete("absenceId");
    setSearchParams(nextParams, { replace: true });
  }, [selectedAbsence?.id]);

  return (
    <AbsenceContext.Provider
      value={{
        selectedAbsence,
        setSelectedAbsence,
        isEditing,
        setIsEditing,
        resetAbsence,
      }}
    >
      {children}
    </AbsenceContext.Provider>
  );
};
