{"name": "worktime", "version": "0.1.0", "private": true, "dependencies": {"@azure/msal-browser": "^3.0.2", "@microsoft/signalr": "^8.0.7", "@react-oauth/google": "^0.11.1", "@reduxjs/toolkit": "^1.9.5", "@swc/core": "^1.3.80", "@swc/jest": "^0.2.29", "@testing-library/jest-dom": "^6.1.2", "@testing-library/react": "^14.0.0", "@testing-library/user-event": "^14.4.3", "@vitejs/plugin-react-swc": "^3.3.2", "jest": "^30.0.5", "jest-environment-jsdom": "^29.6.4", "react": "^18.2.0", "react-dom": "^18.2.0", "react-google-recaptcha": "^3.1.0", "react-redux": "^8.1.2", "react-router-dom": "^6.15.0", "react-toastify": "^9.1.3", "styled-components": "^6.0.7", "vite": "^4.4.5", "web-vitals": "^3.4.0"}, "devDependencies": {"@types/jest": "^29.5.4", "@types/node": "^20.5.4", "@types/react": "^18.2.21", "@types/react-dom": "^18.2.7", "@types/react-google-recaptcha": "^2.1.5", "@types/styled-components": "^5.1.26", "@vitejs/plugin-react": "^4.0.4", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "typescript": "^5.0.2"}, "scripts": {"start": "vite --port 3000", "build": "vite build", "serve": "vite preview", "test": "jest", "eject": "vite eject", "server": "json-server --watch src/features/payrolls/server/db.json"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}