import React from "react";
import styled from "styled-components";
import OkIcon from "../../assets/images/dot-icons/ok.svg";
import XIcon from "../../assets/images/dot-icons/x.svg";
import Translator, { translate } from "../../services/language/Translator";
import EditsButton from "./EditsButton";

interface EditCardProps {
  newValue: string | undefined;
  onCancel?: () => Promise<void>;
  onConfirm?: () => Promise<void>;
}

const CardContainer = styled.div`
  background-color: var(--auth-button-background-color);
  border-radius: 1.5rem;
  padding: 1.5rem;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  max-width: 20rem;
  box-sizing: border-box;
`;

const HeaderText = styled.p`
  margin: 0 0 0.5rem 0;
  font-size: 0.9rem;
  color: var(--combobox-header-text-color);
  font-weight: normal;
`;

const ValueText = styled.p`
  margin: 0 0 1.5rem 0;
  font-size: 1rem;
  color: var(--profile-department-name-font-color);
  font-weight: 500;
  line-height: 1.4;
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 0.2rem;
`;

const ApproveEditCard: React.FC<EditCardProps> = ({
  newValue,
  onCancel,
  onConfirm,
}) => {
  return (
    <CardContainer>
      <HeaderText>
        <Translator getString="New data:" />
      </HeaderText>
      <ValueText>{newValue}</ValueText>
      <ButtonContainer>
        <EditsButton
          variant="cancel"
          onClick={onCancel}
          text={translate("DeclineEdit")}
          icon={XIcon}
          dataTestId="cancel-button"
        />
        <EditsButton
          variant="confirm"
          onClick={onConfirm}
          text={translate("Approve")}
          icon={OkIcon}
          dataTestId="confirm-button"
        />
      </ButtonContainer>
    </CardContainer>
  );
};

export default ApproveEditCard;
