import { useEffect, useRef, useState } from "react";
import styled from "styled-components";

const EmployeeName = styled.span<{ $isDeleted?: boolean }>`
  margin-left: 0.3rem;
  font-size: 0.6rem;
  color: black;
  white-space: nowrap;
  text-decoration: ${(props) => (props.$isDeleted ? "line-through" : "none")};
  max-width: 9rem;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;

  &:hover::after {
    content: attr(data-full-name);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background-color: white;
    color: black;
    padding: 0.3rem 0.5rem;
    border-radius: 0.3rem;
    font-size: 0.7rem;
    white-space: nowrap;
    z-index: 1000;
    pointer-events: none;
    margin-bottom: 0.2rem;
    opacity: 0;
    transition: opacity 0.2s ease;
  }

  &:hover:not([data-truncated="false"])::after {
    opacity: 1;
  }
`;

const EmployeeNameWrapper = ({
  name,
  isDeleted,
}: {
  name: string;
  isDeleted?: boolean;
}) => {
  const ref = useRef<HTMLSpanElement>(null);
  const [isTruncated, setIsTruncated] = useState(false);

  useEffect(() => {
    const el = ref.current;
    if (el) {
      setIsTruncated(el.scrollWidth > el.clientWidth);
    }
  }, [name]);

  return (
    <EmployeeName
      ref={ref}
      $isDeleted={isDeleted}
      data-full-name={name}
      data-truncated={isTruncated ? "true" : "false"}
    >
      {name}
    </EmployeeName>
  );
};

export default EmployeeNameWrapper;
