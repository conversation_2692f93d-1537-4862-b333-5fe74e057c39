import { useMemo } from "react";
import { LightPayrollDTO } from "../../models/DTOs/payrolls/LightPayrollDTO";
import { PayrollDTO } from "../../models/DTOs/payrolls/PayrollDTO";

interface Employee {
  id: string;
  payrolls: LightPayrollDTO[];
  userId: string;
  name: string;
  email: string;
  egn: string;
}

const filterEmployeesWithLeaves = (
  payrolls: LightPayrollDTO[],
  selectedPayroll: PayrollDTO | LightPayrollDTO | undefined,
  firstDayOfMonth: Date,
  lastDayOfMonth: Date
): LightPayrollDTO[] => {
  if (selectedPayroll) {
    var filteredPayrolls = payrolls.filter(
      (payroll) =>
        (payroll.employee.workTimeId !== selectedPayroll.employee.workTimeId ||
          (payroll.employee.workTimeId ===
            selectedPayroll.employee.workTimeId &&
            payroll.id === selectedPayroll.workTimeId)) &&
        payroll.structureLevelId === selectedPayroll.structureLevelId
    );
  } else {
    filteredPayrolls = payrolls;
  }

  return filteredPayrolls.filter((payroll) => {
    return payroll.leaves.some((leave) => {
      const leaveStart = new Date(leave.fromDate);
      const leaveEnd = new Date(leave.toDate);

      return leaveStart <= lastDayOfMonth && leaveEnd >= firstDayOfMonth;
    });
  });
};

const mapToEmployee = (payroll: LightPayrollDTO): Employee => {
  const fullNameParts = [
    payroll.employee.firstName || "",
    payroll.employee.secondName || "",
    payroll.employee.lastName || "",
  ].filter((part) => part && part.trim() !== "");

  return {
    id: payroll.employee.workTimeId,
    payrolls: [payroll],
    userId: payroll.employee.userId,
    name: fullNameParts.join(" ").trim(),
    email: payroll.employee.email,
    egn: payroll.employee.egn,
  };
};

export const useFilteredEmployees = (
  payrolls: LightPayrollDTO[],
  selectedMonth: number,
  selectedYear: number,
  selectedPayroll: PayrollDTO | LightPayrollDTO | undefined
): Employee[] => {
  return useMemo(() => {
    if (!payrolls || payrolls.length === 0) {
      return [];
    }

    const firstDayOfMonth = new Date(selectedYear, selectedMonth, 1);
    const lastDayOfMonth = new Date(selectedYear, selectedMonth + 1, 0);

    const filteredPayrolls = filterEmployeesWithLeaves(
      payrolls,
      selectedPayroll,
      firstDayOfMonth,
      lastDayOfMonth
    );

    const employees = filteredPayrolls.map((emp) => mapToEmployee(emp));

    return employees;
  }, [payrolls, selectedMonth, selectedYear, selectedPayroll]);
};

export const useEmployeesWithLeaves = (
  payrolls: LightPayrollDTO[]
): Employee[] => {
  return useMemo(() => {
    return payrolls.map((payroll) => mapToEmployee(payroll));
  }, [payrolls]);
};

export type { Employee };
