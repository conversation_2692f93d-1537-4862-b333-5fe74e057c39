import { Action, Reducer } from "redux";
import { AppThunk, ClearStateAction, RootState } from "../../app/store";
import { EmployeePayrollDTO } from "../../models/DTOs/payrolls/EmployeePayrollDTO";
import { AddressDTO } from "../../models/DTOs/address/AddressDTO";
import { getEmployeePayrolls } from "../../services/employees/employeePayrollsService";

interface EmployeePayrollsState {
  employeePayrolls: EmployeePayrollDTO[];
}

type KnownActions =
  | LoadEmployeePayrollsAction
  | ClearStateAction
  | AddEmployeePayrollAction
  | UpdateEmployeePayrollAction;

interface AddEmployeePayrollAction {
  type: "ADD_EMPLOYEE_PAYROLL";
  employeePayroll: EmployeePayrollDTO;
}

interface UpdateEmployeePayrollAction {
  type: "UPDATE_EMPLOYEE_PAYROLL";
  employeePayroll: EmployeePayrollDTO;
}

interface LoadEmployeePayrollsAction {
  type: "LOAD_EMPLOYEE_PAYROLLS";
  employeePayrolls: EmployeePayrollDTO[];
}

export const loadEmployeePayrollsAction = (
  employeePayrolls: EmployeePayrollDTO[]
): LoadEmployeePayrollsAction => ({
  type: "LOAD_EMPLOYEE_PAYROLLS",
  employeePayrolls,
});

export const addEmployeePayrollAction = (
  employeePayroll: EmployeePayrollDTO
): AddEmployeePayrollAction => ({
  type: "ADD_EMPLOYEE_PAYROLL",
  employeePayroll,
});

export const updateEmployeePayrollAction = (
  employeePayroll: EmployeePayrollDTO
): UpdateEmployeePayrollAction => ({
  type: "UPDATE_EMPLOYEE_PAYROLL",
  employeePayroll,
});

export const actionCreators = {
  onEmployeePayrollsLoaded: (
    companyId: string
  ): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      companyId &&
        getEmployeePayrolls(companyId).then((employeePayrolls) => {
          dispatch(loadEmployeePayrollsAction(employeePayrolls));
        });
    };
  },
  onEmployeePayrollAdded: (
    employeePayroll: EmployeePayrollDTO
  ): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      dispatch(addEmployeePayrollAction(employeePayroll));
    };
  },
  onEmployeePayrollUpdated: (
    employeePayroll: EmployeePayrollDTO
  ): AppThunk<void, KnownActions> => {
    return async (dispatch: any) => {
      dispatch(updateEmployeePayrollAction(employeePayroll));
    };
  },
};

export const {
  onEmployeePayrollsLoaded,
  onEmployeePayrollAdded,
  onEmployeePayrollUpdated,
} = actionCreators;

const initialState = {
  employeePayrolls: [],
} as EmployeePayrollsState;

export const reducer: Reducer<EmployeePayrollsState> = (
  state = initialState,
  action: Action
) => {
  switch (action.type) {
    case "LOAD_EMPLOYEE_PAYROLLS":
      return {
        ...state,
        employeePayrolls: [
          ...(action as LoadEmployeePayrollsAction).employeePayrolls,
        ],
      };
    case "ADD_EMPLOYEE_PAYROLL":
      return {
        ...state,
        employeePayrolls: [
          ...state.employeePayrolls,
          (action as AddEmployeePayrollAction).employeePayroll,
        ],
      };
    case "UPDATE_EMPLOYEE_PAYROLL": {
      const updatedPayroll = (action as UpdateEmployeePayrollAction)
        .employeePayroll;

      const employeePayrolls = [...state.employeePayrolls];
      const isEmployeeExisting = employeePayrolls.find(
        (p) => p.employeeGuid === updatedPayroll.employeeGuid
      );
      if (isEmployeeExisting !== undefined) {
        return {
          ...state,
          employeePayrolls: employeePayrolls.map((p) =>
            p.employeeGuid === updatedPayroll.employeeGuid ? updatedPayroll : p
          ),
        };
      } else {
        return {
          ...state,
          employeePayrolls: [...employeePayrolls, updatedPayroll],
        };
      }
    }
    case "CLEAR_STATE":
      return initialState;
    default:
      return state;
  }
};

export const selectEmployeePayrolls = (state: RootState) =>
  state.employeePayrolls;
