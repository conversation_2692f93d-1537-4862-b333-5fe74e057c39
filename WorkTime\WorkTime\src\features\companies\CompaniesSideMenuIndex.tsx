import { useEffect, useState } from "react";
import styled, { css } from "styled-components";
import moveRightImage from "../../assets/images/side-menu-buttons/join-active.svg";
import moveRightDisabledImage from "../../assets/images/side-menu-buttons/join-icon.svg";
import moveRightHoverImage from "../../assets/images/side-menu-buttons/join-icon.svg";
import plusImage from "../../assets/images/side-menu-buttons/add-company-active.svg";
import plusDisabledImage from "../../assets/images/side-menu-buttons/add-company.svg";
import plusHoverImage from "../../assets/images/side-menu-buttons/add-company.svg";
import importImage from "../../assets/images/side-menu-buttons/sendera-active.svg";
import importImageDisabled from "../../assets/images/side-menu-buttons/sendera-icon.svg";
import companiesImage from "../../assets/images/side-menu-buttons/companies-active.svg";
import companiesImageDisable from "../../assets/images/side-menu-buttons/companies.svg";
import Container from "../../components/Container";
import Button from "../../components/Inputs/Button";
import { CompanyDTO } from "../../models/DTOs/companies/CompanyDTO";
import CompaniesListSideMenu from "./companiesPage/CompaniesListSideMenu";
import CreateCompany from "./CreateCompany";
import ImportCompany from "./ImportCompany";
import JoinCompany from "./JoinCompany";
import { translate } from "../../services/language/Translator";
import { useUserEmployee } from "../UserEmployeeContext";
import { DefaultPermissions } from "../../constants/permissions";
import { LOCAL_STORAGE_WORKTIME_ROLE_NAME } from "../../constants/local-storage-constants";

const ButtonsContainer = styled(Container)`
  margin-bottom: 0.625rem;
  width: 100%;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-column-gap: 0.5rem;
  grid-row-gap: 0;
`;

const ContentContainer = styled(Container)`
  position: absolute;
  top: 15rem;
  overflow: auto;

  ::-webkit-scrollbar {
    display: none;
  }

  scrollbar-width: none;
  bottom: 3rem;
  left: 1rem;
  right: 1rem;
`;

const CompanyButton = styled(Button)<{
  $companyImage: string;
  $companyHoverImage: string;
  $companyDisabledImage: string;
  $isDisable: boolean;
}>`
  width: 100%;
  background-color: var(--company-button-background-color);
  color: var(--company-button-color);
  font-size: 1rem;
  background-image: url(${(p) => p.$companyImage});
  overflow: hidden;
  background-repeat: no-repeat;
  background-size: 1.6rem;
  background-position: left 1rem center;
  padding: 1rem 0 1rem 3.5rem;
  text-align: left;
  cursor: default;

  ${(p) =>
    p.$isDisable &&
    css`
      background-image: url(${p.$companyDisabledImage});
      background-color: var(--company-button-background-color-disable);
      color: var(--profile-button-color-disable);
      background-position: left 1rem center;
      background-size: 1.6rem;
      background-repeat: no-repeat;
      cursor: pointer;
    `}

  &:hover {
    background-image: url(${(p) => p.$companyHoverImage});
    background-position: left 1rem center;
    background-size: 1.6rem;
    background-repeat: no-repeat;
    cursor: pointer;
    background-color: ${(p) =>
      p.$isDisable
        ? "var(--company-button-background-color-hover)"
        : "var(--company-button-background-color)"};
  }

  @media (max-width: 760px) {
    padding: 1rem 1rem 1rem 3rem;
  }
`;

enum TabItems {
  Import = "import",
  Create = "create",
  Join = "join",
  MyCompanies = "myCompanies",
}

const CompaniesSideMenuIndex = () => {
  const [company, setCompany] = useState({} as CompanyDTO);
  const [activeTab, setActiveTab] = useState("myCompanies");
  const { userEmployee } = useUserEmployee();

  useEffect(() => {
    if (Object.keys(company || {}).length === 0) {
      setCompany({
        name: translate("Import companies"),
        bulstat: "",
        contactName: "",
        userRegistrationCompanyId: 0,
        id: "",
      });
    }
  }, [company]);

  const handleSetTab = (tab: string) => {
    setActiveTab(tab);
  };

  const isOwner =
    localStorage.getItem(LOCAL_STORAGE_WORKTIME_ROLE_NAME) === "grOwner";

  const canAddCompany =
    isOwner ||
    userEmployee.permissions.includes(DefaultPermissions.Companies.Write);
  const canImportCompany =
    isOwner ||
    userEmployee.permissions.includes(DefaultPermissions.Companies.Import);
  const canJoinCompany =
    isOwner ||
    userEmployee.permissions.includes(DefaultPermissions.Companies.Join);

  return (
    <>
      <ButtonsContainer>
        <CompanyButton
          label="Companies list"
          $companyImage={companiesImage}
          companyHoverImage={
            activeTab !== "myCompanies" ? companiesImageDisable : companiesImage
          }
          $companyDisabledImage={companiesImageDisable}
          onClick={() => handleSetTab("myCompanies")}
          $isDisable={activeTab !== "myCompanies"}
          data-testid="my-companies-button"
        />
        {canAddCompany && (
          <CompanyButton
            label="strAddCompanySideMenu"
            $companyImage={plusImage}
            companyHoverImage={
              activeTab !== "create" ? plusHoverImage : plusImage
            }
            $companyDisabledImage={plusDisabledImage}
            onClick={() => handleSetTab("create")}
            $isDisable={activeTab !== "create"}
            data-testid="add-company-button"
          />
        )}
        {canImportCompany && (
          <CompanyButton
            label="Import"
            $companyImage={importImage}
            companyHoverImage={
              activeTab !== "import" ? importImageDisabled : importImage
            }
            $companyDisabledImage={importImageDisabled}
            onClick={() => handleSetTab("import")}
            $isDisable={activeTab !== "import"}
            data-testid="import-button"
          />
        )}
        {canJoinCompany && (
          <CompanyButton
            label="Join company"
            $companyImage={moveRightImage}
            companyHoverImage={
              activeTab !== "join" ? moveRightHoverImage : moveRightImage
            }
            $companyDisabledImage={moveRightDisabledImage}
            onClick={() => handleSetTab("join")}
            $isDisable={activeTab !== "join"}
            data-testid="join-company-button"
          />
        )}
      </ButtonsContainer>
      <ContentContainer>
        {activeTab === TabItems.Import && <ImportCompany />}
        {activeTab === TabItems.MyCompanies && <CompaniesListSideMenu />}
        {activeTab === TabItems.Create && <CreateCompany />}
        {activeTab === TabItems.Join && <JoinCompany />}
      </ContentContainer>
    </>
  );
};

export default CompaniesSideMenuIndex;
