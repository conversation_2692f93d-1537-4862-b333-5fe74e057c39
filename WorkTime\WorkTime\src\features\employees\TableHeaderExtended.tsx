import styled from "styled-components";
import { ColumnDefinitionType } from "../../components/Table/Table";
import ComboboxMultipleChoices from "../../components/Combobox/ComboboxMultipleChoices";
import NestedComboboxMultipleChoices from "../../components/Combobox/NestedComboboxMultipleChoices";
import BaseFilter from "../../components/Table/BaseFilter";
import { BaseEmployeeView } from "./EmployeeViewInterfaces";
import React from "react";
import { StructureLevelDTO } from "../../models/DTOs/companyStructure/StructureLevelDTO";
import { mapDepartmentToNestedOption } from "../../services/departments/departmentService";

interface TableHeaderExtendedProps {
  radioButton?: React.ReactNode;
  columns?: Array<
    ColumnDefinitionType<BaseEmployeeView, keyof BaseEmployeeView>
  >;
  departmentOptions: StructureLevelDTO[];
  categoryOptions: {
    identifier: number;
    name?: string;
    description?: string;
  }[];
  typeOfAppointmentOptions: {
    identifier: number;
    description?: string;
  }[];
  selectedDepartments: string[];
  selectedCategories: string[];
  selectedTypeOfAppointment: string[];
  onDepartmentChange: (selected: any[]) => void;
  onCategoryChange: (selected: any[]) => void;
  onTypeOfAppointmentChange: (selected: any[]) => void;
  onTextFilterChange: (columnKey: string, searchText: string) => void;
}

const TableRow = styled.tr<{ isFirstCellShown: boolean }>`
  display: grid;
  grid-template-columns: ${({ isFirstCellShown }) =>
    isFirstCellShown
      ? "0.05fr 1.5fr 1fr 1fr 1fr 1fr "
      : "0fr 1.5fr 1fr 1fr 1fr 1fr "};
  margin-bottom: -1.1rem;
  border-radius: 1.625rem 1.625rem 0 0;
  background-color: var(--table-row-color);
  grid-gap: 1.5rem;
  padding: 0.3rem 0.8rem 0.1rem 0rem;
`;

const TableHeaderCell = styled.th`
  text-align: center;
  color: var(--table-header-color);
  font-family: segoe-ui;
  font-style: normal;

  &:first-child {
    border-radius: 1.625rem 0 0 0;
    padding-top: 1.5rem;
    max-width: 1.8rem;
  }

  &:nth-child(2) {
    grid-column: 2 / 3;
    min-width: 12rem;
  }

  &:last-child {
    border-radius: 0 1.625rem 0 0;
  }
`;

const TableHeaderExtended = ({
  radioButton,
  departmentOptions,
  categoryOptions,
  typeOfAppointmentOptions,
  selectedDepartments,
  selectedCategories,
  selectedTypeOfAppointment,
  onDepartmentChange,
  onCategoryChange,
  onTypeOfAppointmentChange,
  onTextFilterChange,
}: TableHeaderExtendedProps): JSX.Element => {
  return (
    <thead data-testid="table-header-extended">
      <TableRow data-testid="table-header-row" isFirstCellShown={!!radioButton}>
        <TableHeaderCell data-testid="radio-button-header">
          {radioButton}
        </TableHeaderCell>
        <TableHeaderCell data-testid="personal-data-header">
          <BaseFilter
            data-testid="personal-data-filter"
            showSort={false}
            column={"personalData"}
            placeholder={"strSearchByPersonalData"}
            onFilterChange={onTextFilterChange}
          />
        </TableHeaderCell>
        <TableHeaderCell data-testid="position-header">
          <BaseFilter
            data-testid="position-filter"
            showSort={false}
            column={"position"}
            placeholder={"strSearchByPosition"}
            onFilterChange={onTextFilterChange}
          />
        </TableHeaderCell>
        <TableHeaderCell data-testid="departments-header">
          <NestedComboboxMultipleChoices
            data-testid="departments-combobox"
            options={departmentOptions.map(mapDepartmentToNestedOption)}
            selectedValues={selectedDepartments}
            onChange={onDepartmentChange}
            placeholder="strDepartments"
          />
        </TableHeaderCell>
        <TableHeaderCell data-testid="contract-type-header">
          <ComboboxMultipleChoices
            data-testid="type-of-appointment-combobox"
            options={typeOfAppointmentOptions.map((option) => ({
              id: option.identifier,
              label: option.description || "",
              value: option.identifier,
            }))}
            selectedValues={selectedTypeOfAppointment}
            onChange={onTypeOfAppointmentChange}
            placeholder="Type of appointment"
            conditionalMargin={true}
            idCondition={[9, 10, 11, 12]}
          />
        </TableHeaderCell>
        <TableHeaderCell data-testid="category-header">
          <ComboboxMultipleChoices
            data-testid="category-combobox"
            options={categoryOptions.map((option) => ({
              id: option.identifier,
              label: option.name || "",
              value: option.identifier,
            }))}
            selectedValues={selectedCategories}
            onChange={onCategoryChange}
            placeholder="strCategories"
          />
        </TableHeaderCell>
      </TableRow>
    </thead>
  );
};

export default TableHeaderExtended;
