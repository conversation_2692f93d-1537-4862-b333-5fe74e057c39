import React from "react";
import styled from "styled-components";
import { ChangeEvent, useEffect, useState } from "react";
import Searchbar from "../../../components/Inputs/Searchbar";
import CompaniesGridView from "./CompaniesGridView";
import CompaniesListView from "./CompaniesListView";
import gridView from "../../../assets/images/companiesView/gridviewIcon.png";
import listView from "../../../assets/images/companiesView/listviewIcon.png";
import listIconHover from "../../../assets/images/companiesView/listIconHover.png";
import gridIconHover from "../../../assets/images/companiesView/gridIconHover.png";
import { useAppDispatch, useAppSelector } from "../../../app/hooks";
import { CompanyDTO } from "../../../models/DTOs/companies/CompanyDTO";
import { companiesState, onGetCompanies, Status } from "../companiesActions";
import MainWindowContainer from "../../../components/MainWindowContainer";
import { companyFilterService } from "../../../services/companies/CompanyFilterService";
import { useMenu } from "../../MenuContext";
import Loading from "../../../components/Loading";
import { Header } from "../../../components/Header";
import { HeaderType } from "../../../models/Enums/HeaderType";
import { LOCAL_STORAGE_IS_COMPANIES_VIEW_LIST_VIEW } from "../../../constants/local-storage-constants";
import NoCompaniesIndex from "../NoCompaniesIndex";
import { PendingCompaniesList } from "./PendingCompaniesList";
import { translate } from "../../../services/language/Translator";
import Label from "../../../components/Inputs/Label";

const MainContainer = styled(MainWindowContainer)`
  display: flex;
  flex-direction: column;
  align-items: center;
  margin: 1rem;
  height: 45rem;
  overflow-y: auto;
`;

const Container = styled.div`
  display: flex;
  justify-content: center;
  width: clamp(30%, 90rem, 90%);
  margin: 1rem;
`;

const SearchbarContainer = styled.div`
  display: inline-block;
  position: relative;
  box-sizing: border-box;
  justify-content: center;
  width: clamp(35%, 55rem, 70%);
  margin: 1rem;
`;

const ImageGridListView = styled.div<{ $isListView: boolean }>`
  position: absolute;
  background-size: cover;
  height: 2rem;
  width: 2rem;
  right: 1.5rem;
  top: 50%;
  transform: translateY(-50%);
  background-image: url(${gridView});

  &:hover {
    ${({ $isListView }) =>
      !$isListView
        ? `
    background-image: url(${listIconHover});
  `
        : `
    background-image: url(${gridIconHover});
  `}
  }

  ${({ $isListView }) =>
    !$isListView &&
    `
     background-image: url(${listView});
  `}
`;

const CircleContainer = styled.button`
  --circle-size: 2.2rem;

  height: var(--circle-size);
  width: var(--circle-size);
  background-color: var(--circle-container-backround-gridView);
  border-radius: 50%;
  border: none;
  position: fixed;
  right: 2.5rem;
  bottom: 1rem;
  z-index: 2;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;

  &::before {
    content: "+";
    font-size: 2rem;
    color: var(--circle-container-plus-colort-gridView);
  }

  &:hover {
    background-color: var(--circle-container-hover-backround-gridView);
  }
`;

const StyledHeader = styled(Header)`
  margin-top: 3.5rem;
  width: 20rem;
  text-align: center;
  font-weight: 500;
  font-size: 22px;
`;

const CompanyName = styled(Label)`
  font: Segoe UI;
  color: var(--listview-text);
  opacity: 1;
  font-size: 1.2rem;
  text-align: left;
  cursor: pointer;
  vertical-align: middle;
  overflow: hidden;
`;

const Companies = () => {
  const [textInput, setTextInput] = useState("");
  const [isListView, setIsListView] = useState<boolean>(
    localStorage.getItem(LOCAL_STORAGE_IS_COMPANIES_VIEW_LIST_VIEW) === "true"
  );
  const { activeCompanies, pendingCompanies, status } =
    useAppSelector(companiesState);
  const { companyFilter, mapToFilteredDTOs } = companyFilterService();
  const [filteredCompanies, setFilteredCompanies] = useState<CompanyDTO[]>(
    companyFilter(textInput)
  );
  const { changeView, toggleMenu } = useMenu();
  const dispatch = useAppDispatch();

  useEffect(() => {
    dispatch(onGetCompanies());
  }, [dispatch]);

  useEffect(() => {
    const filteredCompaniesArray = companyFilter(textInput);
    setFilteredCompanies(filteredCompaniesArray);
  }, [activeCompanies, pendingCompanies]);

  const handleSetViewModeClick = () => {
    localStorage.setItem(
      LOCAL_STORAGE_IS_COMPANIES_VIEW_LIST_VIEW,
      `${!isListView}`
    );

    setIsListView(!isListView);
  };

  const companiesFilter = (searchText: string) => {
    const filteredCompaniesArray = companyFilter(searchText);
    const mappedFilteredData = mapToFilteredDTOs(filteredCompaniesArray);
    setFilteredCompanies(mappedFilteredData);
  };

  const handleChange = (e: ChangeEvent<HTMLInputElement>) => {
    const searchText = e.target.value;
    setTextInput(searchText);
    companiesFilter(searchText);
  };

  const handleAddCompany = () => {
    toggleMenu();
    changeView("add-company");
  };

  if (status === Status.Fetching) return <Loading />;

  if (
    status === Status.Fetched &&
    activeCompanies.length === 0 &&
    pendingCompanies.length === 0
  )
    return <NoCompaniesIndex />;

  return (
    <MainContainer data-testid="main-container">
      <StyledHeader
        content="strWelcomeToWorkTimeHome"
        headerType={HeaderType.H2}
        data-testid="header"
      />
      <SearchbarContainer data-testid="searchbar-container">
        <Searchbar
          handleChange={handleChange}
          value={textInput}
          type="text"
          label="strGetInput"
          placeholder={""}
          data-testid="searchbar"
        />
        <ImageGridListView
          $isListView={isListView}
          onClick={handleSetViewModeClick}
          data-testid="image-grid-list-view"
        />
      </SearchbarContainer>

      <Container data-testid="companies-container">
        {isListView ? (
          <CompaniesListView
            data={filteredCompanies}
            data-testid="companies-list-view"
          />
        ) : (
          <CompaniesGridView
            data={filteredCompanies}
            data-testid="companies-grid-view"
          />
        )}
        <CircleContainer
          onClick={handleAddCompany}
          data-testid="circle-container"
        />
      </Container>
      {filteredCompanies.length === 0 && pendingCompanies.length === 0 && (
        <CompanyName>{translate("No companies found")}</CompanyName>
      )}

      <PendingCompaniesList
        pendingCompanies={pendingCompanies}
        data-testid="pending-companies-list"
      />
    </MainContainer>
  );
};
export default Companies;
