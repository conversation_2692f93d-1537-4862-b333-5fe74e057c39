import { AddressDTO } from "../address/AddressDTO";
import { EmployeeDTO } from "../employees/EmployeeDTO";
import { PayrollPersonalDataDTO } from "./payrollPersonalDataDTO";

export interface PersonalInformationDTO {
  iban: string;
  code: string;
  employee: EmployeeDTO;
  employeePreviousValues: EmployeeDTO | null;
  editType: EditType | null;
  addresses: AddressDTO[];
  payrollPersonalData: PayrollPersonalDataDTO;
}

export enum EditType {
  UserEdit = 0,
  AdminEdit = 1,
}
